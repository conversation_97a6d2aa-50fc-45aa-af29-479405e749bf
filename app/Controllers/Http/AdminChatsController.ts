import Event from '@ioc:Adonis/Core/Event'
import { schema } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Chat from 'App/Models/Chat'
import ChatHistory from 'App/Models/ChatHistory'
import { rules } from '@ioc:Adonis/Core/Validator'
import User from 'App/Models/User'

export default class AdminChatsController {
  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')
    const result = await Chat.query()
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await Chat.query().where('id', id).preload('user').first()
    if (!result) {
      return response.notFound()
    }
    return response.ok({ data: result })
  }

  public async create({ response, request, auth }: HttpContextContract) {
    const postSchema = schema.create({
      content: schema.string(),
      email: schema.string([
        rules.exists({
          table: 'users',
          column: 'email',
        }),
      ]),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const user = await User.query()
        .where('email', payload.email)
        .whereNot((query) => query.has('admin'))
        .first()

      if (!user) {
        return response.badRequest({ success: false, message: 'Invalid user' })
      }

      const chat = await Chat.create({ userId: user.id })
      const history = new ChatHistory()

      history.merge({
        userId: auth.user?.id,
        content: payload.content,
        chatId: chat.id,
        createdAt: chat.createdAt,
      })
      await history.save()
      Event.emit('chat:create', chat.id)
      return response.created({ data: chat, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async createHistory({ request, response, params: { id }, auth }: HttpContextContract) {
    const postSchema = schema.create({
      content: schema.string([rules.trim()]),
    })

    try {
      const chat = await Chat.query().where('id', id).first()
      if (!chat) {
        return response.notFound()
      }
      const payload = await request.validate({ schema: postSchema })

      const history = await ChatHistory.create({
        ...payload,
        userId: auth.user!.id,
        chatId: chat.id,
      })

      Event.emit('history:create', history.id)
      return response.created({ data: history, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async findHistories({ request, response, params: { id } }: HttpContextContract) {
    const chat = await Chat.query().where('id', id).first()
    if (!chat) {
      return response.notFound()
    }

    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')
    const result = await ChatHistory.query()
      .where('chat_id', id)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }
}
