import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Event from '@ioc:Adonis/Core/Event'
import Logger from '@ioc:Adonis/Core/Logger'
import { RequestContract } from '@ioc:Adonis/Core/Request'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import { string } from '@ioc:Adonis/Core/Helpers'
import Auth from 'App/Models/Auth'
import User from 'App/Models/User'
import UserEvent from 'App/Models/UserEvent'
import { DateTime } from 'luxon'
import Child from 'App/Models/Child'
import AppleAuth from 'apple-auth'
import fs from 'fs'
import jwt from 'jsonwebtoken'
import axios from 'axios'
import Wallet from 'App/Models/Wallet'

const removeDuplicateApiToken = async (token: any) => {
  try {
    // kick user from multiple login
    const lastToken = await Database.from('api_tokens')
      .where('user_id', token.user.id)
      .orderBy('id', 'desc')
      .where('name', token.name)
      .first()
    await Database.from('api_tokens')
      .where('user_id', token.user.id)
      .whereNot('id', lastToken.id)
      .where('name', token.name)
      .delete()

    return true
  } catch (error) {
    Logger.debug('Delete token issue : %o', error)
    return false
  }
}

const subscribeToMailerLite = ({ email, region }: { email: string; region: string }) => {
  console.log(region)
  // if (region === 'tw') {
  //   Event.emit('auth:register', {
  //     email,
  //     group: '*****************', // "TAIWAN MVT 2. Create Account Haven't Tried"
  //     // group: '*****************', // "Testing (Taiwan New Users)"
  //   })

  //   return
  // }

  Event.emit('auth:register', {
    email,
    // group: '*****************', // "Create account on Mobile app (havent tried)"
    group: '138756461302908776', // "Create App on Mobile (Nov 2024)"
  })
}

export const logUserEvent = async (
  userId: number,
  provider: string,
  request: RequestContract,
  ipAddress: string,
  location: string,
  type: string
) => {
  const device = request.header('user-agent')?.match(/\(([^)]+)\)/)
  const deviceName = device ? device[1] : request.header('user-agent')
  await UserEvent.create({
    type: type,
    device: deviceName,
    ipAddress: ipAddress,
    location: location,
    userId: userId,
    provider: provider,
  })
}

export default class AuthController {
  public async register({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string.optional({ trim: true }, [
        rules.email(),
        rules.maxLength(255),
        rules.unique({ table: 'users', column: 'email' }),
        rules.requiredIfNotExists('phone'),
      ]),
      // username: schema.string.optional({ trim: true }, [
      //   rules.unique({ table: 'users', column: 'username' }),
      //   rules.regex(/^[a-zA-Z0-9]+$/),
      // ]),
      password: schema.string({ trim: true }, [rules.confirmed()]),
      auth_code: schema.string({ trim: false }, []),
      //   referral_code: schema.string.optional({ trim: false }, [
      //     rules.exists({
      //       table: 'users',
      //       column: 'referral_code',
      //     }),
      //   ]),
      phone: schema.string.optional({ trim: true }, [
        rules.mobile(),
        rules.requiredIfNotExists('email'),
      ]),
      full_name: schema.string.optional({}),
      gender: schema.string.optional({}, []),
      nationality: schema.string.optional({}, []),
      birthdate: schema.date.optional(),
      setting: schema.object.optional().members({
        receiveUpdate: schema.boolean.optional(),
      }),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    var { setting, ...input } = validationData

    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ minutes: -15 }).toISO()

    // can register with either email or phone
    if (validationData.email != null) {
      const checkEmailRequest = await Auth.query()
        .where('email', validationData.email)
        .where('code', validationData.auth_code)
        .where('created_at', '>', ago)
        .where('created_at', '<', now)
        .where('status', 1)
        .first()

      if (checkEmailRequest == null) {
        return response.status(400).send({
          message: 'Code is invalid or expired, please verify your email first',
          code: 'code.invalid',
          success: false,
        })
      }
    } else if (validationData.phone) {
      // TODO: check if phone is duplicated
    }

    // let parentUser: User | null
    // if (referral_code != null) {
    //   parentUser = await User.query()
    //     .where('referral_code', referral_code)
    //     .where('verified', true)
    //     .first()
    //   if (parentUser == null) {
    //     return response.status(400).send({
    //       message: 'Referral is not verified',
    //       code: 'referral.is.not.verified',
    //       success: false,
    //     })
    //   }
    // }

    const createNewUser = await Database.transaction(async (trx) => {
      const newUser = new User()
      if (validationData.email) {
        newUser.email = input.email!
      } else {
        // newUser.phone = input.phone!
      }
      // if (validationData.username) {
      //   newUser.username = input.username!
      // }
      newUser.password = input.password
      //   newUser.fullName = input.full_name
      //   newUser.provider = 'email'
      //   if (input.phone) newUser.phone = input.phone
      //   if (parentUser) newUser.parentId = parentUser.id
      newUser.useTransaction(trx)
      await newUser.save()

      //create user setting
      await newUser.related('userSetting').firstOrCreate({
        ...validationData.setting,
      })

      // create entry in referral table
      //   if (newUser.parentId) {
      //     const newReferral = new Referral()
      //     newReferral.userId = newUser.parentId
      //     newReferral.childId = newUser.id
      //     newReferral.targetAmount = 10000 // TODO: hard code target amount
      //     newReferral.status = 'pending'
      //     newReferral.useTransaction(trx)
      //     await newReferral.save()
      //   }

      // TODO
      // if (newUser.parentId) {
      //   const selfReward = new Reward()
      //   selfReward.type = 'referral'
      //   selfReward.points = 10
      //   selfReward.userId = newUser.id
      //   selfReward.fromUserId = newUser.parentId
      //   selfReward.useTransaction(trx)
      //   await selfReward.save()

      //   const referralReward = new Reward()
      //   referralReward.type = 'referral'
      //   referralReward.points = 20
      //   referralReward.userId = newUser.parentId
      //   referralReward.fromUserId = newUser.id
      //   referralReward.useTransaction(trx)
      //   await referralReward.save()
      // }

      // create wallet
      const newWallet = new Wallet()
      newWallet.userId = newUser.id
      newWallet.useTransaction(trx)
      await newWallet.save()

      // const freeCreditTrx = new Transaction()
      // freeCreditTrx.userId = user.id
      // freeCreditTrx.walletId = newWallet.id
      // freeCreditTrx.type = 'bonus'
      // freeCreditTrx.title = 'Registration Reward'
      // freeCreditTrx.description = 'Free Credits'
      // freeCreditTrx.amount = 5
      // freeCreditTrx.amountIn = 5
      // freeCreditTrx.amountOut = 0
      // freeCreditTrx.status = 'confirmed'
      // freeCreditTrx.useTransaction(trx)
      // await freeCreditTrx.save()

      return {
        success: true,
        data: newUser,
      }
    })

    if (createNewUser.success) {
      subscribeToMailerLite({ email: createNewUser.data.email, region: createNewUser.data.region })

      return response.send({
        success: true,
        data: await User.query().where('id', createNewUser.data.id).first(),
      })
    } else {
      return response.status(400).send({
        success: false,
        code: 'register.failed',
        message: 'Register failed',
      })
    }
  }

  public async forgotPassword({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [
        rules.email(),
        rules.maxLength(255),
        rules.exists({ table: 'users', column: 'email' }),
      ]),
      password: schema.string({ trim: true }, [rules.confirmed()]),
      auth_code: schema.string({ trim: false }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const input = {
      email: validationData.email,
      password: validationData.password,
    }

    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ minutes: -15 }).toISO()
    const checkEmailRequest = await Auth.query()
      .where('email', input.email)
      .where('code', validationData.auth_code)
      .where('created_at', '>', ago)
      .where('created_at', '<', now)
      .where('status', 1)
      .first()

    if (checkEmailRequest == null) {
      return response.status(400).send({
        message: 'Code is invalid or expired, please verify your email first',
        success: false,
        data: {},
      })
    }

    const user = await User.findByOrFail('email', input.email)
    user.password = input.password
    await user.save()

    return response.send({
      success: true,
      data: user,
    })
  }

  public async changePassword({ request, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth?.user?.id)
    const validationSchema = schema.create({
      password: schema.string({ trim: true }, []),
      new_password: schema.string({ trim: true }, [rules.confirmed()]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    if (!(await User.verifyPassword(validationData.password, user.password))) {
      return response.status(400).send({
        code: 'password.change.failed.password.not.matched',
        message: 'Password not match',
      })
    }

    if (await User.verifyPassword(validationData.new_password, user.password)) {
      return response.status(400).send({
        code: 'password.change.failed.password.same',
        message: 'Repeated password is not allowed',
      })
    }

    user.password = validationData.new_password
    await user.save()
    await auth.logout()
    return response.send({
      success: true,
    })
  }

  public async sendAuthCode({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    const input = {
      email: validationData.email,
    }

    let email = input.email
    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ seconds: 15 }).toISO()
    const code = (Math.floor(Math.random() * 10000) + 10000).toString().substring(1)

    console.log(email, code)
    const checkEmailRequest = await Auth.query()
      .where('email', email)
      .whereBetween('created_at', [ago, now])
      .where('status', 0)
      .first()

    if (checkEmailRequest != null) {
      Logger.info('Send auth code email : %s. Please try again after 15 seconds', email)
      return response.send({
        message: 'Please try again after 15 seconds',
        code: 'auth.email.time.limit',
        success: false,
      })
    }

    const newAuth = await Auth.create({
      email: email,
      code: code,
      status: 0,
      type: 0,
      expiredAt: DateTime.local().plus({ minutes: 15 }),
    })

    Event.emit('auth:code', {
      ...newAuth.toJSON(),
      title: 'Email Verification',
      code: newAuth.code,
    })

    return response.send({
      success: true,
    })
  }

  public async sendAuthCodeForgetPassword({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    const input = {
      email: validationData.email,
    }

    let email = input.email
    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ minutes: -1 }).toISO()

    const code = string.generateRandom(6)
    const findUser = await User.findBy('email', email)
    if (findUser == null) {
      return response.status(400).send({
        message: 'Email is not registered',
      })
    }
    const checkEmailRequest = await Auth.query()
      .where('email', email)
      .whereBetween('created_at', [ago, now])
      .where('status', 0)
      .first()

    if (checkEmailRequest != null) {
      Logger.info('send auth code email : %s . Please try again after 1 min', email)
      return response.send({
        message: 'Please try again after 1 min',
        code: 'auth.email.time.limit',
        success: false,
      })
    }

    const newAuth = await Auth.create({
      email: email,
      code: code,
      status: 0,
      type: 1,
      expiredAt: DateTime.local().plus({ minutes: 15 }),
    })

    Event.emit('auth:code', {
      ...newAuth.toJSON(),
      title: 'Forgot Password',
      code: newAuth.code,
    })

    return response.send({
      success: true,
    })
  }

  public async verifyAuthCode({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
      code: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    const input = {
      email: validationData.email,
      code: validationData.code,
    }

    const now = DateTime.local().toSQL()

    const checkEmailRequest = await Auth.query()
      .where('email', input.email)
      .where('code', input.code)
      .where('expired_at', '>', now)
      .where('status', 0)
      .first()

    if (checkEmailRequest == null) {
      return response.send({
        message: 'Code is invalid or expired',
        code: 'auth.email.code.invalid',
        success: false,
        data: {},
      })
    }

    await Auth.query().where('id', checkEmailRequest?.id).update({
      status: 1,
    })

    return response.send({
      success: true,
    })
  }

  public async login({ request, auth, response }: HttpContextContract) {
    console.log(request.all())
    const validationSchema = schema.create({
      email: schema.string.optional({ trim: true }, [
        rules.email(),
        rules.requiredIfNotExists('username'),
      ]),
      username: schema.string.optional({ trim: true }, [rules.requiredIfNotExists('email')]),
      password: schema.string({ trim: true }, []),
      ip_address: schema.string.optional({ trim: true }, []),
      location: schema.string.optional({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    const { email, username, password } = validationData
    // call login common function
    try {
      let identifier = ''
      if (email != null) {
        identifier = email
      } else if (username != null) {
        identifier = username
      }
      const token = await auth.use('api').attempt(identifier, password, {
        expiresIn: '3days',
        name: 'Opaque Access Token',
      })

      await removeDuplicateApiToken(token)

      const user = await User.query().where('id', token.user.id).firstOrFail()
      // user.lastLoginAt = DateTime.local()
      await user.save()

      await logUserEvent(
        user.id,
        'email',
        request,
        validationData?.ip_address ?? 'Unknown',
        validationData?.location ?? 'Unknown',
        'login'
      )

      Logger.info('DEBUG: JWT auth token : %s %o ', user.email, token.toJSON())

      return response.status(200).send({
        message: 'Login successfully',
        code: 'login.success',
        success: true,
        data: {
          jwt: token.toJSON(),
          user: user,
        },
      })
    } catch (error) {
      console.log(error)
      Logger.error('Login error %o', error)
      return response.status(400).send({
        success: false,
        code: error.code,
        message: 'login failed',
        data: null,
      })
    }
  }

  public async loginMagic({ auth, request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
      code: schema.string({ trim: true }, []),
      region: schema.string.optional(),
      name: schema.string.optional(),
      child_name: schema.string.optional(),
      child_dob: schema.date.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })
    // const now = DateTime.local().toSQL()

    const checkEmailRequest = await Auth.query()
      .where('email', validationData.email)
      .where('code', validationData.code)
      // .where('expired_at', '>', now)
      .where('status', 0)
      .first()

    if (checkEmailRequest === null) {
      return response.send({
        message: 'Code is invalid or expired',
        code: 'auth.email.code.invalid',
        success: false,
        data: {},
      })
    }

    await Auth.query().where('id', checkEmailRequest?.id).update({
      status: 1,
    })

    const user = await User.query().where('email', validationData.email).first()

    if (user !== null) {
      if (user.blocked) {
        return response.status(400).send({
          message: 'Failed to authenticate.',
        })
      }

      const token = await auth.use('api').login(user, {
        expiresIn: '3days',
        name: 'Opaque Access Token',
      })

      await logUserEvent(user.id, 'email', request, 'Unknown', 'Unknown', 'login')

      return response.send({
        message: 'Login successfully',
        success: true,
        jwt: token.toJSON(),
        user: user,
      })
    } else {
      const createNewUser = await Database.transaction(async (trx) => {
        // create user
        const newUser = new User()
        newUser.password = string.generateRandom(16)
        if (validationData.email) {
          newUser.email = validationData.email!
        }
        if (validationData.region) {
          newUser.region = validationData.region
        }
        if (validationData.name) {
          newUser.name = validationData.name
        }
        newUser.useTransaction(trx)
        await newUser.save()

        // create wallet
        const newWallet = new Wallet()
        newWallet.userId = newUser.id
        newWallet.useTransaction(trx)
        await newWallet.save()

        // const freeCreditTrx = new Transaction()
        // freeCreditTrx.userId = user.id
        // freeCreditTrx.walletId = newWallet.id
        // freeCreditTrx.type = 'bonus'
        // freeCreditTrx.title = 'Registration Reward'
        // freeCreditTrx.description = 'Free Credits'
        // freeCreditTrx.amount = 5
        // freeCreditTrx.amountIn = 5
        // freeCreditTrx.amountOut = 0
        // freeCreditTrx.status = 'confirmed'
        // freeCreditTrx.useTransaction(trx)
        // await freeCreditTrx.save()

        // create child
        const newChild = new Child()
        if (validationData.child_name) {
          newChild.name = validationData.child_name
        }
        if (validationData.child_dob) {
          newChild.birthdate = validationData.child_dob
        }
        newChild.userId = newUser.id
        newChild.useTransaction(trx)
        await newChild.save()

        // set child as active
        newUser.activeChildId = newChild.id
        newUser.useTransaction(trx)
        await newUser.save()

        return newUser
      })

      if (createNewUser === null) {
        return response.status(400).send({
          code: 'register.failed',
          message: 'Register failed',
        })
      }

      Event.emit('plan:trial', {
        email: createNewUser.email,
      })

      await logUserEvent(createNewUser!.id, 'email', request, 'Unknown', 'Unknown', 'login')

      const token = await auth.use('api').login(createNewUser, {
        expiresIn: '3days',
        name: 'Opaque Access Token',
      })

      subscribeToMailerLite({ email: createNewUser.email, region: validationData.region ?? '' })

      return response.send({
        user: await User.query().where('id', createNewUser.id).first(),
        success: true,
        jwt: token.toJSON(),
        message: 'Register successfully',
        code: 'register.successful',
      })
    }
  }

  public async loginGoogle({ auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      access_token: schema.string(),
      region: schema.string(),
      name: schema.string.optional(),
      child_name: schema.string.optional(),
      child_dob: schema.date.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const profile = await axios({
        url: 'https://www.googleapis.com/oauth2/v2/userinfo',
        method: 'get',
        headers: {
          Authorization: `Bearer ${validationData.access_token}`,
        },
      })

      if (profile.data?.email) {
        const user = await User.query().where('email', profile.data.email).first()

        if (user !== null) {
          if (user.blocked) {
            return response.status(400).send({
              message: 'Failed to authenticate.',
            })
          }

          const token = await auth.use('api').login(user, {
            expiresIn: '3days',
            name: 'Opaque Access Token',
          })

          await logUserEvent(user.id, 'email', request, 'Unknown', 'Unknown', 'login')

          return response.send({
            message: 'Login successfully',
            success: true,
            jwt: token.toJSON(),
            user: user,
          })
        } else {
          const createNewUser = await Database.transaction(async (trx) => {
            // create user
            const newUser = new User()
            newUser.password = string.generateRandom(16)
            newUser.email = profile.data.email
            if (validationData.region) {
              newUser.region = validationData.region
            }
            if (validationData.name) {
              newUser.name = validationData.name
            }
            newUser.useTransaction(trx)
            await newUser.save()

            // create wallet
            const newWallet = new Wallet()
            newWallet.userId = newUser.id
            newWallet.useTransaction(trx)
            await newWallet.save()

            // const freeCreditTrx = new Transaction()
            // freeCreditTrx.userId = user.id
            // freeCreditTrx.walletId = newWallet.id
            // freeCreditTrx.type = 'bonus'
            // freeCreditTrx.title = 'Registration Reward'
            // freeCreditTrx.description = 'Free Credits'
            // freeCreditTrx.amount = 5
            // freeCreditTrx.amountIn = 5
            // freeCreditTrx.amountOut = 0
            // freeCreditTrx.status = 'confirmed'
            // freeCreditTrx.useTransaction(trx)
            // await freeCreditTrx.save()

            // create child
            const newChild = new Child()
            if (validationData.child_name) {
              newChild.name = validationData.child_name
            }
            if (validationData.child_dob) {
              newChild.birthdate = validationData.child_dob
            }
            newChild.userId = newUser.id
            newChild.useTransaction(trx)
            await newChild.save()

            // set child as active
            newUser.activeChildId = newChild.id
            newUser.useTransaction(trx)
            await newUser.save()
            return newUser
          })

          if (createNewUser === null) {
            return response.status(400).send({
              code: 'register.failed',
              message: 'Register failed',
            })
          }

          Event.emit('plan:trial', {
            email: createNewUser.email,
          })

          await logUserEvent(createNewUser!.id, 'email', request, 'Unknown', 'Unknown', 'login')

          const token = await auth.use('api').login(createNewUser, {
            expiresIn: '3days',
            name: 'Opaque Access Token',
          })

          subscribeToMailerLite({ email: profile.data.email, region: validationData.region })

          return response.send({
            user: await User.query().where('id', createNewUser.id).first(),
            success: true,
            jwt: token.toJSON(),
            message: 'Register successfully',
            code: 'register.successful',
          })
        }
      } else {
        return response.status(400).send({
          message: 'Failed to authenticate',
        })
      }
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        message: 'Failed to authenticate.',
      })
    }
  }

  public async loginApple({ auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      access_token: schema.string(),
      region: schema.string(),
      first_name: schema.string.optional(),
      last_name: schema.string.optional(),
      child_name: schema.string.optional(),
      child_dob: schema.date.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const appleAuth = new AppleAuth(
        {
          client_id: `${process.env.APPLE_CLIENT_ID}`,
          team_id: `${process.env.APPLE_TEAM_ID}`,
          key_id: `${process.env.APPLE_KEY_ID}`,
          redirect_uri: `${process.env.PROXY_HOST}/api/v1/callback/login/apple`,
          scope: 'name email',
        },
        fs.readFileSync(`${process.env.APPLE_APPLICATION_CREDENTIALS}`).toString(),
        'text'
      )
      const appleAccess = await appleAuth.accessToken(validationData.access_token)
      const profile: any = jwt.decode(appleAccess.id_token)

      if (profile.email) {
        const user = await User.query().where('email', profile.email).first()

        if (user !== null) {
          if (user.blocked) {
            return response.status(400).send({
              message: 'Failed to authenticate.',
            })
          }

          const token = await auth.use('api').login(user, {
            expiresIn: '3days',
            name: 'Opaque Access Token',
          })

          await logUserEvent(user.id, 'email', request, 'Unknown', 'Unknown', 'login')

          return response.send({
            message: 'Login successfully',
            success: true,
            jwt: token.toJSON(),
            user: user,
          })
        } else {
          const createNewUser = await Database.transaction(async (trx) => {
            // create user
            const newUser = new User()
            newUser.password = string.generateRandom(16)
            newUser.email = profile.email
            if (validationData.region) {
              newUser.region = validationData.region
            }
            if (profile.name) {
              newUser.name = profile.name
            }
            newUser.useTransaction(trx)
            await newUser.save()

            // create wallet
            const newWallet = new Wallet()
            newWallet.userId = newUser.id
            newWallet.useTransaction(trx)
            await newWallet.save()

            // const freeCreditTrx = new Transaction()
            // freeCreditTrx.userId = user.id
            // freeCreditTrx.walletId = newWallet.id
            // freeCreditTrx.type = 'bonus'
            // freeCreditTrx.title = 'Registration Reward'
            // freeCreditTrx.description = 'Free Credits'
            // freeCreditTrx.amount = 5
            // freeCreditTrx.amountIn = 5
            // freeCreditTrx.amountOut = 0
            // freeCreditTrx.status = 'confirmed'
            // freeCreditTrx.useTransaction(trx)
            // await freeCreditTrx.save()

            // create child
            const newChild = new Child()
            if (validationData.child_name) {
              newChild.name = validationData.child_name
            }
            if (validationData.child_dob) {
              newChild.birthdate = validationData.child_dob
            }
            newChild.userId = newUser.id
            newChild.useTransaction(trx)
            await newChild.save()

            // set child as active
            newUser.activeChildId = newChild.id
            newUser.useTransaction(trx)
            await newUser.save()
            return newUser
          })

          if (createNewUser === null) {
            return response.status(400).send({
              code: 'register.failed',
              message: 'Register failed',
            })
          }

          Event.emit('plan:trial', {
            email: createNewUser.email,
          })

          await logUserEvent(createNewUser!.id, 'email', request, 'Unknown', 'Unknown', 'login')

          const token = await auth.use('api').login(createNewUser, {
            expiresIn: '3days',
            name: 'Opaque Access Token',
          })

          subscribeToMailerLite({ email: profile.email, region: validationData.region })

          return response.send({
            user: await User.query().where('id', createNewUser.id).first(),
            success: true,
            jwt: token.toJSON(),
            message: 'Register successfully',
            code: 'register.successful',
          })
        }
      } else {
        return response.status(400).send({
          message: 'Failed to authenticate email',
          code: 'auth.email.failed',
        })
      }
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        message: 'Failed to authenticate.',
        code: 'auth.failed',
      })
    }
  }

  public async refreshToken({ auth, response }: HttpContextContract) {
    try {
      const user = await User.findOrFail(auth?.user?.id)
      const token = await auth.use('api').generate(user)
      await auth.logout()

      return response.send({
        success: true,
        data: {
          jwt: token.toJSON(),
          user: user,
        },
      })
    } catch (error) {
      Logger.error('refresh token issue : %o', error)

      return response
        .status(400)
        .send({ success: false, code: 'refresh.token.failed', message: 'refresh failed' })
    }
  }
}
