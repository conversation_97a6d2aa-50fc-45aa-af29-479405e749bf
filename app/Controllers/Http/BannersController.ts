import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Banner from 'App/Models/Banner'
import _, { keys } from 'lodash'

export default class BannersController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    if (!keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    const banners = await Banner.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

    return response.status(200).send(banners)
  }

  public async adminFind({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const banners = await Banner.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

    return response.status(200).send(banners)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const banner = await Banner.query().where('id', params.id).first()

    return response.status(200).send({ data: banner })
  }

  public async create({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      thumbnail_url: schema.string({ trim: true }),
      cta_url: schema.string.optional({ trim: true }),
      region: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const createBanner = await Banner.create({
      thumbnailUrl: validationData.thumbnail_url,
      ctaUrl: validationData.cta_url ?? '',
      region: validationData.region,
    })

    return response.status(200).send({ success: true, data: createBanner })
  }

  public async update({ request, response, params }: HttpContextContract) {
    const validationSchema = schema.create({
      thumbnail_url: schema.string.optional({ trim: true }),
      cta_url: schema.string.optional({ trim: true }),
      region: schema.string.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const updateBanner = await Banner.findOrFail(params.id)
    updateBanner.merge({
      thumbnailUrl: validationData.thumbnail_url,
      ctaUrl: validationData.cta_url ?? '',
      region: validationData.region,
    })
    await updateBanner.save()

    return response.status(200).send({ success: true, data: updateBanner })
  }

  public async delete({ response, params }: HttpContextContract) {
    const deleteBanner = await Banner.query().where('id', params.id).delete()

    return response.status(200).send({
      success: true,
      data: deleteBanner,
    })
  }
}
