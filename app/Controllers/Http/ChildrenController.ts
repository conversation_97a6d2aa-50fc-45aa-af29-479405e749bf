import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Child from 'App/Models/Child'
import _ from 'lodash'

export default class ChildrenController {
  public async findMyChildren({ auth, response, request }: HttpContextContract) {
    const user = await auth.authenticate()
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    console.log(user.id)
    const children = await Child.filter(filters)
      .where('user_id', user.id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(children)
  }

  public async findChildren({ response, request, params }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const children = await Child.filter(filters)
      .where('user_id', params.user_id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(children)
  }

  public async addChild({ response, request, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    const validateDataSchema = schema.create({
      name: schema.string(),
      birthdate: schema.date(),
    })
    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const child = await Child.create({ ...validateData, userId: user.id })

    return response.status(200).send({ success: true, data: child })
  }

  public async editChild({ response, request, params, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    const validateDataSchema = schema.create({
      name: schema.string.optional(),
      birthdate: schema.date.optional(),
    })
    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const findChild = await Child.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()
    findChild.merge(validateData)
    await findChild.save()

    return response.status(200).send({ success: true, data: findChild })
  }

  public async removeChild({ response, params, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    const findChild = await Child.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()

    await findChild.delete()

    return response.status(200).send({ success: true, data: findChild })
  }
}
