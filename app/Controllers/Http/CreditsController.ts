import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Credit from 'App/Models/Credit'

export default class CreditsController {
  public async findAll({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'rank:asc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
      const credits = await Credit.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.status(200).send(credits)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  public async update({ request, response, params }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        title: schema.string(),
        description: schema.string.optional(),
        amount: schema.number(),
        price: schema.number(),
        compare_at: schema.number()
      })

      const validationData = await request.validate({ schema: validationSchema })
      const findCredit = await Credit.findOrFail(params.id)

      const result = await Database.transaction(async (trx) => {
        findCredit.title = validationData.title
        if (validationData.description) {
          findCredit.description = validationData.description
        } else {
          findCredit.description = ''
        }
        findCredit.amount = validationData.amount
        findCredit.price = validationData.price
        findCredit.compareAt = validationData.compare_at
        findCredit.useTransaction(trx)
        await findCredit.save()

        return findCredit
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }
}
