import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Device from 'App/Models/Device'
import User from 'App/Models/User'

export default class DevicesController {
  public async createMyToken({ response, request, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      token: schema.string.optional({ trim: true }),
      platform: schema.enum(['ios', 'android', 'huawei'] as const),
      device_no: schema.string({ trim: true }, []),
      model: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const user = await User.findOrFail(auth?.user?.id)
    console.log('debug device: ', validationData)
    console.log('user id : ', user.id)
    const device = await Device.updateOrCreate(
      {
        userId: user.id,
        platform: validationData.platform,
        deviceNo: validationData.device_no,
        model: validationData.model
      },
      {
        token: validationData.token,
      }
    )
    return response.send({
      success: true,
      data: await Device.query().where('id', device.id).first(),
    })
  }
}
