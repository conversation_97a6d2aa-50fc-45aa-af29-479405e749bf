import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import PackCode from 'App/Models/PackCode'
import _ from 'lodash'
import Randomstring from 'randomstring'

export default class PackCodesController {
  public async findActivePackCodes({ response }: HttpContextContract) {
    const activeCodes = await Database.from('pack_codes AS a')
      .select(['b.title', 'b.language', 'b.region', 'a.pack_code'])
      .leftJoin('packs AS b', 'a.pack_id', 'b.id')
      .whereNull('a.user_id')
      .orderBy('b.title')
      .orderBy('b.region')
      .orderBy('b.language')

    return response.send(activeCodes)
  }

  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const packCodes = await PackCode.filter(filters)
      .preload('pack')
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(packCodes)
  }

  public async create({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        pack_id: schema.number([rules.exists({ table: 'packs', column: 'id' })]),
        number_of_pack_codes: schema.number(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      for (let i = 1; i <= validationData.number_of_pack_codes; i++) {
        let uid: string
        let isUnique = true
        do {
          uid = Randomstring.generate(6)
          const checkCode = await PackCode.findBy('pack_code', uid)
          if (checkCode) {
            isUnique = false
          } else isUnique = true
        } while (!isUnique)

        await Database.transaction(async (trx) => {
          const newPackCode = new PackCode()
          newPackCode.packId = validationData.pack_id
          newPackCode.packCode = uid
          newPackCode.useTransaction(trx)
          await newPackCode.save()

          return newPackCode
        })
      }

      return response.status(200).send({ success: true, message: 'Successfullt generated' })
    } catch (error) {
      console.log(error)
    }
  }
}
