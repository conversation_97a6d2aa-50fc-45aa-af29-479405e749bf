import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import stripe from 'stripe'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Plan, { PlanType } from 'App/Models/Plan'
import Subscription from 'App/Models/Subscription'
import StoryOrder from 'App/Models/StoryOrder'
import User from 'App/Models/User'
import _ from 'lodash'
import { uploadToS3Bucket } from './FilesController'
import Database from '@ioc:Adonis/Lucid/Database'
import PlanPricing from 'App/Models/PlanPricing'
import Logger from '@ioc:Adonis/Core/Logger'
import Story from 'App/Models/Story'
import Setting from 'App/Models/Setting'
import { DateTime } from 'luxon'

async function createCoupon(
  percentOff: number,
  duration: stripe.CouponCreateParams.Duration
): Promise<string> {
  const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

  const coupon = await stripeInstance.coupons.create({
    percent_off: percentOff,
    duration: duration,
  })

  return coupon.id
}

export default class PlansController {
  public async findStripeProducts({ response }: HttpContextContract) {
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

    const products = await stripeInstance.products.list({
      active: true,
      expand: ['data.default_price'],
    })
    // const prices = await stripeInstance.prices.list({
    //   active: true,
    //   type: 'recurring',
    //   expand: ['data.product'],
    // })

    // remove products with non-recurring prices
    const plans = products.data.filter(
      (product) => (product.default_price as stripe.Price).type == 'recurring'
    )

    return response.ok({ data: plans })
  }

  public async findStripeInvoices({ response }: HttpContextContract) {
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

    const invoices = await stripeInstance.invoices.list()
    // const prices = await stripeInstance.prices.list({
    //   active: true,
    //   type: 'recurring',
    //   expand: ['data.product'],
    // })

    return response.ok({ data: invoices })
  }

  // TODO: cannot buy same plan twice, if the plan is still active
  public async createStripeCheckout({ auth, request, response }: HttpContextContract) {
    console.log('create stripe checkout', request.all())
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)
    const validationSchema = schema.create({
      plan_pricing_id: schema.number([rules.exists({ table: 'plan_pricings', column: 'id' })]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const user = await auth.authenticate()
    const planPricing = await PlanPricing.findOrFail(validationData.plan_pricing_id)
    planPricing.load('plan')

    // check if we are applying any discount
    let hasDiscount = false
    let discountPercentage = 0

    // 1. check if welcome discount is active
    const welcomeDiscount = await Setting.findBy('handle', 'welcome-discount')
    console.log('has welcome discount', welcomeDiscount?.toJSON())

    // 2. check if user is still within discount period
    const today = DateTime.now()
    if (welcomeDiscount && user.promotionEndedAt > today) {
      hasDiscount = true
      discountPercentage = parseFloat(welcomeDiscount.value)
      console.log('discount 1', discountPercentage)
    } else if (planPricing.promotionEndedAt > today) {
      // 3. check if this plan has it's own discount
      hasDiscount = true
      discountPercentage = planPricing.discount
      console.log('discount 2', discountPercentage)
    }

    /// NOTE: welcome discount has precedent over plan discount

    let couponId: string | null = null
    if (hasDiscount) {
      couponId = await createCoupon(discountPercentage, 'once')
    }

    console.log('coupon', couponId)
    const params: stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      line_items: [
        {
          price: planPricing.stripePriceId as string,
          quantity: 1,
        },
      ],
      allow_promotion_codes: true,
      success_url: process.env.STRIPE_SUCCESS_URL,
      cancel_url: process.env.STRIPE_CANCEL_URL,
    }

    if (couponId) {
      params['discounts'] = [{ coupon: couponId }]
    }

    if (user.stripeCustomerId) {
      try {
        // double check if user exist in stripe
        await stripeInstance.customers.retrieve(user.stripeCustomerId)
        params['customer'] = user.stripeCustomerId
      } catch (error) {
        params['customer_email'] = user.email
      }
    } else {
      params['customer_email'] = user.email
    }

    const session = await stripeInstance.checkout.sessions.create(params)

    return response.ok({ success: true, data: session })
  }

  public async findPlans({ auth, request, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (user == null) {
      const plans = await Plan.filter(filters)
        .where('published', true)
        .preload('pricings')
        .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(plans)
    }

    const plans = await Plan.filter(filters)
      .where('published', true)
      .preload('pricings')
      .preload('planStories', (query) =>
        query
          .withCount('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false).as('redeemed')
          )
          .preload('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false)
          )
          .orderBy('pivot_ordering', 'asc')
      )
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(plans)
  }

  public async findOnePlan({ auth, params, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    if (user == null) {
      const plan = await Plan.query()
        .where('id', params.id)
        .preload('pricings')
        .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
        .first()

      return response.ok({ data: plan })
    }

    const plan = await Plan.query()
      .where('id', params.id)
      .preload('pricings')
      .preload('planStories', (query) =>
        query
          .withCount('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false).as('redeemed')
          )
          .preload('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false)
          )
          .orderBy('pivot_ordering', 'asc')
      )
      .first()

    return response.ok({ data: plan })
  }

  public async findPlanByHandle({ auth, params, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    if (user == null) {
      const plan = await Plan.query()
        .where('handle', params.handle)
        .preload('pricings')
        .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
        .first()

      return response.ok({ data: plan })
    }

    const plan = await Plan.query()
      .where('handle', params.handle)
      .preload('pricings')
      .preload('planStories', (query) =>
        query
          .withCount('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false).as('redeemed')
          )
          .preload('storyOrders', (query) =>
            query.where('user_id', user.id).where('blocked', false)
          )
          .orderBy('pivot_ordering', 'asc')
      )
      .first()

    return response.ok({ data: plan })
  }

  public async findMySubscriptions({ auth, request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    // const filters = omit(request.all(), ['page', 'limit', 'sort'])

    const user = await auth.authenticate()
    const subscriptions = await Subscription.query()
      .where('user_id', user.id)
      .whereIn('status', ['trailing', 'active']) // TODO: do we want to show inactive subscriptions?
      .whereNull('end_date')
      .preload('plan', (query) => {
        query.preload('planStories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .preload('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false)
            )
            .orderBy('pivot_ordering', 'asc')
        )
      })
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(subscriptions)
  }

  public async findMySubscription({ auth, response, params: { id } }: HttpContextContract) {
    const user = await auth.authenticate()
    const subscription = await Subscription.query()
      .where('id', id)
      .where('user_id', user.id)
      .whereIn('status', ['trailing', 'active']) // TODO: do we want to show inactive subscriptions?
      .whereNull('end_date')
      .preload('plan', (query) => {
        query.preload('planStories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .preload('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false)
            )
            .orderBy('pivot_ordering', 'asc')
        )
      })
      .first()

    return response.ok({ data: subscription })
  }

  public async findMySubscriptionWithPlan({ auth, response, params: { id } }: HttpContextContract) {
    const user = await auth.authenticate()
    const subscription = await Subscription.query()
      .where('plan_id', id)
      .where('user_id', user.id)
      .whereIn('status', ['trailing', 'active']) // TODO: do we want to show inactive subscriptions?
      .whereNull('end_date')
      .preload('plan', (query) => {
        query.preload('planStories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .preload('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false)
            )
            .orderBy('pivot_ordering', 'asc')
        )
      })
      .first()

    return response.ok({ data: subscription })
  }

  public async findMyStoryOrder({ auth, params, response }: HttpContextContract) {
    const user = await auth.authenticate()

    // TODO: do we assume the user has a subscription?
    // TODO: what to do with voucher_id

    const storyOrder = await StoryOrder.firstOrCreate(
      {
        storyId: params.story_id,
        userId: user.id,
      },
      {
        storyId: params.story_id,
        userId: user.id,
      }
    )

    return response.ok({ data: storyOrder })
  }

  public async findPlanStories({ auth, request, response, params }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'pivot_ordering:asc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    const featured = request.input('featured', false)

    const plan = await Plan.query().where('id', params.id).first()

    if (!plan) {
      return response.notFound()
    }

    if (user == null) {
      const stories = await plan
        .related('planStories')
        .query()
        .if(filters.level, (query) => {
          query.where('stories.level', filters.level)
        })
        .if(filters.tags, (query) => {
          query.whereHas('tags', (tagsQuery) => {
            tagsQuery.whereIn('tags.id', Array.isArray(filters.tags) ? filters.tags : [filters.tags])
          })
        })
        .if(filters.title, (query) => {
          query.where('title', 'LIKE', `%${filters.title}%`)
        })
        .if(featured, (query) => {
          query.wherePivot('is_featured', true)
        })
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(
        stories.serialize({
          fields: {
            omit: ['ordering', 'is_featured'], // remove ordering & is_featured of 'old' data
          },
        })
      )
    }

    const stories = await plan
      .related('planStories')
      .query()
      .if(filters.level, (query) => {
        query.where('stories.level', filters.level)
      })
      .if(filters.tags, (query) => {
        query.whereHas('tags', (tagsQuery) => {
          tagsQuery.whereIn('tags.id', Array.isArray(filters.tags) ? filters.tags : [filters.tags])
        })
      })
      .if(filters.title, (query) => {
        query.where('title', 'LIKE', `%${filters.title}%`)
      })
      .if(featured, (query) => {
        query.wherePivot('is_featured', true)
      })
      .withCount('storyOrders', (query) =>
        query.where('user_id', user.id).where('blocked', false).as('redeemed')
      )
      .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(
      stories.serialize({
        fields: {
          omit: ['ordering', 'is_featured'], // remove ordering & is_featured of 'old' data
        },
      })
    )
  }

  // TODO: check ios/android for subscription webhooks
  // TODO: admin create/update plan

  // ------------------------------
  // admin
  // ------------------------------

  public async adminFind({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const plans = await Plan.filter(filters)
      .preload('pricings')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(plans)
  }

  public async adminFindOne({ params, response }: HttpContextContract) {
    const plan = await Plan.query().where('id', params.id).preload('pricings').first()

    return response.ok({ data: plan })
  }

  public async adminFindPlanStories({ params, request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'pivot_ordering:asc').split(':')
    // const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    const plan = await Plan.query().where('id', params.id).first()

    if (!plan) {
      return response.notFound()
    }

    const stories = await plan
      .related('planStories')
      .query()
      .preload('chapters')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(
      stories.serialize({
        fields: {
          omit: ['ordering', 'is_featured'], // remove ordering & is_featured of 'old' data
        },
      })
    )
  }

  public async adminFindAllPlanStories({ params, response }: HttpContextContract) {
    const plan = await Plan.query().where('id', params.id).first()

    if (!plan) {
      return response.notFound()
    }

    const stories = await Database.query()
      .from('plan_stories')
      .where('plan_id', params.id)
      .select('story_id')

    return response.ok({ data: stories })
  }

  public async create({ auth, request, response }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      title: schema.string(),
      handle: schema.string(),
      description: schema.string(),
      // stripePriceId: schema.string(),
      // stripeProductId: schema.string(),
      // price: schema.number(),
      region: schema.array().members(schema.string()),
      language: schema.string(),
      blocked: schema.boolean.optional(),
      published: schema.boolean.optional(),
      thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    let src: string | null = null
    if (validationData.thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      src = uploadBucket.url
    }

    const createPlan = await Plan.create({
      ...validationData,
      thumbnailUrl: src,
    })

    return response.created({ success: true, data: createPlan })
  }

  public async update({ auth, params, request, response }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const validationSchema = schema.create({
      title: schema.string.optional(),
      handle: schema.string.optional(),
      description: schema.string.optional(),
      // stripePriceId: schema.string.optional(),
      // stripeProductId: schema.string.optional(),
      language: schema.string.optional(),
      region: schema.array.optional().members(schema.string.optional()),
      icon: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      thumbnail: schema.file.optional({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      type: schema.enum.optional(Object.values(PlanType)),
      blocked: schema.boolean.optional(),
      published: schema.boolean.optional(),
    })

    const { region, thumbnail, icon, ...validationData } = await request.validate({
      schema: validationSchema,
    })

    const plan = await Plan.findOrFail(params.id)

    let src: string | null = null
    if (thumbnail) {
      const uploadBucket = await uploadToS3Bucket(
        thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      src = uploadBucket.url
    }

    let iconSrc: string | null = null
    if (icon) {
      const uploadBucket = await uploadToS3Bucket(
        icon,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      iconSrc = uploadBucket.url
    }

    plan.merge({
      ...validationData,
      thumbnailUrl: src,
      iconUrl: iconSrc,
    })

    if (region) {
      plan.region = region as any
    }

    await plan.save()

    return response.ok({ success: true, data: plan })
  }

  public async updatePlanStories({ request, response, params }: HttpContextContract) {
    console.log('updatePlanStories', request.all())
    const validationSchema = schema.create({
      stories: schema
        .array()
        .members(schema.number([rules.exists({ column: 'id', table: 'stories' })])),
    })
    const validationData = await request.validate({ schema: validationSchema })
    const plan = await Plan.findOrFail(params.id)

    await Database.transaction(async (trx) => {
      // if there is a new story, create a new story order for every user that has subscribed to this plan
      // find the difference between original stories and new stories
      const originalStories = await plan.useTransaction(trx).related('planStories').query()
      const newStories = validationData.stories.filter((storyId) => {
        return !originalStories.some((story) => story.id === storyId)
      })

      console.log('update new stories', newStories)

      // find all users that have subscribed to this plan
      const subscriptions = await Subscription.query()
        .where('plan_id', plan.id)
        .whereIn('status', ['trailing', 'active']) // TODO: do we want to show inactive subscriptions?
        .useTransaction(trx)

      console.log('subscriptions', subscriptions)

      if (newStories.length > 0) {
        for (let storyId of newStories) {
          const story = await Story.findOrFail(storyId)

          // loop each user & create story order
          for (let subscription of subscriptions) {
            await StoryOrder.firstOrCreate(
              {
                storyId: story.id,
                userId: subscription.userId,
              },
              {
                storyId: story.id,
                userId: subscription.userId,
              },
              { client: trx }
            )
          }
        }
      }

      // sync the new stories to the plan
      await plan.useTransaction(trx).related('planStories').sync(validationData.stories)

      // // find original stories of this plan
      // const originalStories = await Story.query().where('plan_id', plan.id)
      // // if original stories not in validationData.stories
      // // delete original stories
      // for (let story of originalStories) {
      //   if (!validationData.stories.includes(story.id)) {
      //     story.planId = null
      //     story.useTransaction(trx)
      //     await story.save()
      //   }
      // }
      // // if original stories already include validationData.stories, remove them from stories to be updated
      // const storiesToBeUpdated = validationData.stories.filter((storyId) => {
      //   return !originalStories.some((story) => story.id === storyId)
      // })
      // for (let storyId of storiesToBeUpdated) {
      //   const story = await Story.findOrFail(storyId)
      //   story.planId = plan.id
      //   story.useTransaction(trx)
      //   await story.save()
      // }
    })

    return response.ok({ success: true })
  }

  public async planStoriesSorting({ params, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        sorting_list: schema.array().members(schema.number()), // array of story_ids
      })

      const validationData = await request.validate({ schema: validationSchema })
      const plan = await Plan.findOrFail(params.id)

      const sortingList = validationData.sorting_list

      const syncPayload = {}
      for (let i = 0; i < sortingList.length; i++) {
        const storyId = sortingList[i]
        const ordering = i + 1
        const payload = {}
        payload['ordering'] = ordering
        const key = [storyId]
        // @ts-ignore
        syncPayload[key] = payload
      }

      await plan.related('planStories').sync(syncPayload, false)

      return response.status(200).send({
        success: true,
        message: 'Sort successfully',
      })
    } catch (error) {
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async updatePlanStoriesLevel({ params, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        story_id: schema.number([rules.exists({ column: 'id', table: 'stories' })]),
        level: schema.number(),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const plan = await Plan.findOrFail(params.id)

      const syncPayload = {}
      const payload = {}
      payload['level'] = validationData.level
      const key = [validationData.story_id]
      // @ts-ignore
      syncPayload[key] = payload

      await plan.related('planStories').sync(syncPayload, false)

      return response.status(200).send({
        success: true,
        message: 'Update successfully',
      })
    } catch (error) {
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async updatePlanStoriesFeatured({ params, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        story_id: schema.number([rules.exists({ column: 'id', table: 'stories' })]),
        is_featured: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const plan = await Plan.findOrFail(params.id)

      const syncPayload = {}
      const payload = {}
      payload['is_featured'] = validationData.is_featured
      const key = [validationData.story_id]
      // @ts-ignore
      syncPayload[key] = payload

      await plan.related('planStories').sync(syncPayload, false)

      return response.status(200).send({
        success: true,
        message: 'Update successfully',
      })
    } catch (error) {
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async updatePlanStoriesFree({ params, request, response }: HttpContextContract) {
    Logger.info('updatePlanStoriesFree', request.all())

    try {
      const validationSchema = schema.create({
        story_id: schema.number([rules.exists({ column: 'id', table: 'stories' })]),
        is_free: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const plan = await Plan.findOrFail(params.id)

      const syncPayload = {}
      const payload = {}
      payload['is_free'] = validationData.is_free
      const key = [validationData.story_id]
      // @ts-ignore
      syncPayload[key] = payload

      await plan.related('planStories').sync(syncPayload, false)

      return response.status(200).send({
        success: true,
        message: 'Update successfully',
      })
    } catch (error) {
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async updatePlanPricing({ params, request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      discount: schema.number(),
      promotion_ended_at: schema.date(),
    })

    const validationData = await request.validate({ schema: validationSchema })
    const planPricing = await PlanPricing.findOrFail(params.id)

    planPricing.merge(validationData)
    await planPricing.save()

    return response.ok({ success: true, data: planPricing })
  }

  public async addStoriesToPlan({ params, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        story_list: schema.array().members(
          schema.number([rules.exists({ table: 'stories', column: 'id' })])
        ),
        is_free: schema.boolean.optional(),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const plan = await Plan.findOrFail(params.id)

      await Database.transaction(async (trx) => {
        const stories = await Story.query()
          .whereIn('id', validationData.story_list)
          .useTransaction(trx)

        for (let story of stories) {
          // add story to plan stories
          await Database.table('plan_stories').insert({
            plan_id: plan.id,
            story_id: story.id,
            is_free: validationData.is_free ?? false,
            created_at: new Date(),
            updated_at: new Date(),
          })

          // find all active subscribers of this plan
          const subscriptions = await Subscription.query()
            .whereIn('status', ['trialing', 'active'])
            .where('plan_id', plan.id)
            .useTransaction(trx)

          // create story orders for all active subscribers
          for (let subscription of subscriptions) {
            await StoryOrder.firstOrCreate(
              {
                storyId: story.id,
                userId: subscription.userId,
              },
              {
                storyId: story.id,
                userId: subscription.userId,
              },
              { client: trx }
            )
          }
        }
      })

      return response.ok({
        success: true,
        message: `Successfully added ${validationData.story_list.length} stories to plan`
      })
    } catch (error) {
      console.error(error)
      return response.badRequest({
        success: false,
        message: 'Failed to add stories to plan',
        error: error.message
      })
    }
  }

  public async removeStoryFromPlan({ params, response }: HttpContextContract) {
    try {
      const plan = await Plan.findOrFail(params.id)

      await Database.transaction(async (trx) => {
        // Only remove stories from plan_stories table
        await plan.useTransaction(trx).related('planStories').detach([params.story_id])
      })

      return response.ok({
        success: true,
        message: `Successfully removed story ${params.story_id} from plan`
      })
    } catch (error) {
      console.error(error)
      return response.badRequest({
        success: false,
        message: 'Failed to remove stories from plan',
        error: error.message
      })
    }
  }

  // public async updateSubscriptionBlocked({ request, response }: HttpContextContract) {
  //   const validationSchema = schema.create({
  //     user_id: schema.number([rules.exists({ column: 'id', table: 'users' })]),
  //     subscription_id: schema.number([rules.exists({ column: 'id', table: 'subscriptions' })]),
  //     blocked: schema.boolean(),
  //   })
  //   const validationData = await request.validate({ schema: validationSchema })

  //   await Database.transaction(async (trx) => {
  //     if (validationData.blocked) {
  //       const subscriptions = await Subscription.query()
  //         .where('user_id', validationData.user_id)
  //         .where('subscription_id', validationData.subscription_id)

  //       for (let subscription of subscriptions) {
  //         subscription.blocked = true
  //         subscription.useTransaction(trx)
  //         await subscription.save()
  //       }
  //     } else {
  //       const subscriptions = await Subscription.query()
  //         .where('user_id', validationData.user_id)
  //         .where('subscription_id', validationData.subscription_id)
  //         .where('status', 'active')

  //       await Subscription.updateOrCreateMany(
  //         ['id', 'userId'],
  //         subscriptions.map((subscription) => ({
  //           id: subscription.id,
  //           userId: validationData.user_id,
  //           blocked: false,
  //         })),
  //         { client: trx }
  //       )
  //     }
  //   })

  //   return response.ok({ success: true })
  // }
}
