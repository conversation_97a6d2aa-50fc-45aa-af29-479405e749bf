import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Preschool from 'App/Models/Preschool'
import User from 'App/Models/User'
import _ from 'lodash'

export default class PreschoolsController {
  public async findAll({ request, response, auth }: HttpContextContract) {
    try {
      const user = await User.find(auth.user?.id ?? 0)

      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

      if (user == null) {
        const preschools = await Preschool.query()
          .where('blocked', false)
          .preload('stories', (query) => query.orderBy('ordering', 'asc'))
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
        return response.ok(preschools)
      }

      const preschools = await Preschool.query()
        .where('blocked', false)
        .preload('stories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .orderBy('ordering', 'asc')
        )
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(preschools)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async adminFind({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      //   const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

      const preschools = await Preschool.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(preschools)
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async adminFindOne({ response, params: { id } }: HttpContextContract) {
    try {
      const preschools = await Preschool.query().where('id', id).preload('stories').first()

      return response.ok({ data: preschools })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        title: schema.string(),
        description: schema.string.optional(),
        region: schema.array().members(schema.string()),
        language: schema.string(),
        blocked: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const result = await Database.transaction(async (trx) => {
        const newPreschool = new Preschool()
        newPreschool.title = validationData.title
        if (validationData.description) {
          newPreschool.description = validationData.description
        }
        newPreschool.region = validationData.region
        newPreschool.language = validationData.language
        newPreschool.blocked = validationData.blocked

        newPreschool.useTransaction(trx)
        await newPreschool.save()

        return newPreschool
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    try {
      const preschool = await Preschool.findOrFail(id)
      const validationSchema = schema.create({
        title: schema.string(),
        description: schema.string.optional(),
        region: schema.array().members(schema.string()),
        language: schema.string(),
        blocked: schema.boolean(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const result = await Database.transaction(async (trx) => {
        preschool.title = validationData.title
        if (validationData.description) {
          preschool.description = validationData.description
        }
        preschool.region = validationData.region
        preschool.language = validationData.language
        preschool.blocked = validationData.blocked

        preschool.useTransaction(trx)
        await preschool.save()

        return preschool
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
