import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import PurchaseClickTracker from 'App/Models/PurchaseClickTracker'
import User from 'App/Models/User'

export default class PurchaseClickTrackersController {
  public async clickTracker({ request, response, auth }: HttpContextContract) {
    try {
      const user = await User.findOrFail(auth.user?.id)
      const validationSchema = schema.create({
        story_id: schema.number([rules.exists({ table: 'stories', column: 'id' })]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      await Database.transaction(async (trx) => {
        await PurchaseClickTracker.updateOrCreate(
          {
            storyId: validationData.story_id,
            userId: user.id,
          },
          {},
          { client: trx }
        )
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      console.log(error)
    }
  }
}
