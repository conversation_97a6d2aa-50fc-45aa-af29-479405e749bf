import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import UserEvent from 'App/Models/UserEvent'
import RecordingResult from 'App/Models/RecordingResult'
// import Review from 'App/Models/Review'
import User from 'App/Models/User'
import { isEmpty, last } from 'radash'
import { GoogleSpreadsheet } from 'google-spreadsheet'
import _ from 'lodash'
import Database from '@ioc:Adonis/Lucid/Database'
import PurchaseClickTracker from 'App/Models/PurchaseClickTracker'
import StoryOrder from 'App/Models/StoryOrder'

// const generateUserReport = async (doc: any, region: string) => {
//   const sheet = doc.sheetsByIndex[1] // or use doc.sheetsById[id] or doc.sheetsByTitle[title]
//   // find last row
//   await sheet.loadCells('AQ1:AQ1')
//   const lastRow = await sheet.getCellByA1('AQ1')

//   const users = await User.query()
//     .where('region', region)
//     .preload('children')
//     .preload('devices')
//     .orderBy('id', 'asc')
//     .offset(lastRow.value)

//   await sheet.addRows(
//     users.map((row) => {
//       return {
//         name: row.name,
//         email: row.email,
//         // children: first(row.children)?.name,
//         birthdate:
//           first(row.children)?.birthdate && first(row.children)?.birthdate!.toFormat('dd/MM/yyyy'),
//         registered: row.createdAt && row.createdAt!.toFormat('dd/MM/yyyy'),
//         region: row.region,
//         device: row.devices.map((device) => device.model).join(' | '),
//       }
//     })
//   )
// }

// const generateRatingsReport = async (doc: any, region: string) => {
//   const sheet = doc.sheetsByIndex[5]
//   // find last row
//   await sheet.loadCells('L1:L1')
//   const lastRow = await sheet.getCellByA1('L1')

//   const reviews = await Review.query()
//     .whereHas('user', (query) => query.where('region', region))
//     .preload('user')
//     .preload('activity', (query) => query.preload('playlist'))
//     .orderBy('id', 'asc')
//     .offset(lastRow.value)

//   await sheet.addRows(
//     reviews.map((row) => {
//       return {
//         email: row.user?.email,
//         score: row.score,
//         content: row.content,
//         playlist: row.activity?.playlist?.title,
//         activity: row.activity?.name,
//         want_more: row.moreActivity ? 'Yes' : 'No',
//       }
//     })
//   )
// }

const generateLoginActivitiesReport = async (doc: any, region: string) => {
  const sheet = doc.sheetsByIndex[2]
  // find last row
  await sheet.loadCells('L1:L1')
  const lastRow = await sheet.getCellByA1('L1')

  const loginActivities = await UserEvent.query()
    .where('type', 'login')
    .whereHas('user', (query) => query.where('region', region))
    .preload('user')
    .orderBy('id', 'asc')
    .offset(lastRow.value)

  try {
    await sheet.addRows(
      loginActivities.map((row) => {
        return {
          email: row.user?.email,
          date: row.createdAt.toFormat('dd/MM/yyyy hh:mm:ss'),
        }
      })
    )
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${loginActivities.length} rows.`
}

const generateRecordingsReport = async (doc: GoogleSpreadsheet, region: string) => {
  const sheet = doc.sheetsByIndex[3]
  const totalSheetRows = sheet.rowCount
  // find last row
  await sheet.loadCells('AB1:AB1')
  const lastRow = await sheet.getCellByA1('AB1')
  const language = region == 'sg' ? 'zh' : 'en'
  const offset = (lastRow.value as number) ?? 0

  console.log('------')
  console.log('generate recording reports')
  console.log('region', region)
  console.log('language', language)
  console.log('last row', lastRow.value)
  console.log('------')

  const recordingResults = await RecordingResult.query()
    // .whereHas('user', (query) => query.where('region', region))
    .where('language', 'LIKE', `${language}%`) // zh- or en-
    .preload('user')
    .preload('story')
    .preload('chapter')
    .preload('session', (query) => query.preload('recordingResults'))
    .orderBy('id', 'asc')
    .offset(offset)
    .limit(1000)

  console.log('total sheet rows', totalSheetRows)
  console.log('total row used up', offset + 1)
  console.log('adding new rows', recordingResults.length)
  console.log('------')

  const finalTotal = offset + 1 + recordingResults.length
  if (finalTotal > totalSheetRows) {
    const missingRows = finalTotal - totalSheetRows
    const nextIndex = totalSheetRows
    const lastIndex = totalSheetRows + missingRows
    console.log('Append new rows', missingRows)
    await sheet.insertDimension('ROWS', { startIndex: nextIndex, endIndex: lastIndex })
  }

  await sheet.loadCells(`A${offset + 2}:P${recordingResults.length + offset + 1}`)

  try {
    for (let index = 0; index < recordingResults.length; index++) {
      const row = recordingResults[index]

      const currentIndex = offset + index + 2 // index starts with 2
      const emailCell = await sheet.getCellByA1(`A${currentIndex}`)
      const storyCell = await sheet.getCellByA1(`B${currentIndex}`)
      const chapterCell = await sheet.getCellByA1(`C${currentIndex}`)
      const optionCell = await sheet.getCellByA1(`D${currentIndex}`)
      const answerCell = await sheet.getCellByA1(`E${currentIndex}`)
      const pinyinCell = await sheet.getCellByA1(`F${currentIndex}`)
      const scoreCell = await sheet.getCellByA1(`G${currentIndex}`)
      const languageCell = await sheet.getCellByA1(`H${currentIndex}`)
      const calibrationsCell = await sheet.getCellByA1(`I${currentIndex}`)
      const volumesCell = await sheet.getCellByA1(`J${currentIndex}`)
      const hasRecordingCell = await sheet.getCellByA1(`K${currentIndex}`)
      const isFirstChapterCell = await sheet.getCellByA1(`L${currentIndex}`)
      const isLastRecordingCell = await sheet.getCellByA1(`M${currentIndex}`)
      const devicesCell = await sheet.getCellByA1(`N${currentIndex}`)
      const dateCell = await sheet.getCellByA1(`O${currentIndex}`)
      const levelCell = await sheet.getCellByA1(`P${currentIndex}`)

      emailCell.value = row.user?.email
      storyCell.value = row.story?.title
      chapterCell.value = row.chapter?.title
      optionCell.value = row.category
      answerCell.value = row.hypothesis
      pinyinCell.value = row.pinyinHypothesis
      scoreCell.value = row.hypothesisScore
      languageCell.value = row.language
      calibrationsCell.value = row.calibrations?.join(' | ') ?? ''
      volumesCell.value = row.volumes?.join(' | ') ?? ''
      hasRecordingCell.value = row.fileId === null ? 'no' : 'yes'
      isFirstChapterCell.value = row.story?.defaultChapterId === row.chapterId ? 'yes' : 'no'
      isLastRecordingCell.value =
        row.session.endedAt != null &&
        !isEmpty(row.session.recordingResults) &&
        row.id == last(row.session.recordingResults)!.id
          ? 'yes'
          : 'no'
      devicesCell.value = row.device
      dateCell.value = row.createdAt.toFormat('yyyy/MM/dd')
      levelCell.value = row.story?.level ?? undefined
    }
    await sheet.saveUpdatedCells()
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${recordingResults.length} rows.`

  // await sheet.addRows(
  //   recordingResults.map((row) => {
  //     return {
  //       user: row.user?.email,
  //       chapter: row.chapter?.title,
  //       story: row.story?.title,
  //       option: row.category,
  //       answer: row.hypothesis,
  //       pinyin: row.pinyinHypothesis,
  //       score: row.hypothesisScore,
  //       language: row.language,
  //       calibrations: row.calibrations?.join(' | ') ?? '',
  //       volumes: row.volumes?.join(' | ') ?? '',
  //       has_recording: row.fileId === null ? 'no' : 'yes',
  //       is_first_chapter: row.story?.defaultChapterId === row.chapterId ? 'yes' : 'no',
  //       is_last_recording:
  //         row.session.endedAt != null &&
  //         !isEmpty(row.session.recordingResults) &&
  //         row.id == last(row.session.recordingResults)!.id
  //           ? 'yes'
  //           : 'no',
  //       device: row.device,
  //       date: row.createdAt.toFormat('yyyy/MM/dd'),
  //       level: row.story.level ?? undefined,
  //       // 'ios/ android':
  //       //   row.device.includes('iPhone') || row.device.includes('iPad') ? 'iOS' : 'Android',
  //     }
  //   })
  // )
}

const generateMasterReport = async (doc: GoogleSpreadsheet, region: string) => {
  const masterSheet = doc.sheetsByIndex[1]
  const removeSheet = doc.sheetsByIndex[4]
  const removeRows = await removeSheet.getRows()
  const removeEmails = removeRows.map((row) => row._rawData[0])

  let regions: string[] = []
  if (region == 'sg') {
    regions = ['sg', 'ww']
  } else {
    regions = [region]
  }

  const users = await User.query()
    .whereIn('region', regions)
    .preload('activeChild')
    .preload('devices', (query) => query.distinct('model'))
    .orderBy('id', 'asc')
  const uniqueEmails = users.filter(
    (user) => removeEmails.findIndex((email) => email === user.email) === -1
  )

  await masterSheet.loadCells(`A2:F${uniqueEmails.length + 1}`)
  // uniqueEmails.forEach(async (email, index) => {
  //   const cell = await masterSheet.getCellByA1(`B${index + 2}`) // index starts with 2
  //   cell.value = email.email
  // })

  try {
    for (let index = 0; index < uniqueEmails.length; index++) {
      const element = uniqueEmails[index]
      const nameCell = await masterSheet.getCellByA1(`A${index + 2}`) // index starts with 2
      nameCell.value = element.name
      const emailCell = await masterSheet.getCellByA1(`B${index + 2}`) // index starts with 2
      emailCell.value = element.email
      const birthdateCell = await masterSheet.getCellByA1(`C${index + 2}`) // index starts with 2
      if (element.activeChild?.birthdate != null) {
        birthdateCell.value = element.activeChild?.birthdate
          .setZone('Asia/Kuala_Lumpur')
          .toFormat('yyyy-MM-dd HH:mm:ss')
      }
      const registeredCell = await masterSheet.getCellByA1(`D${index + 2}`) // index starts with 2
      registeredCell.value = element.createdAt
        .setZone('Asia/Kuala_Lumpur')
        .toFormat('yyyy-MM-dd HH:mm:ss')
      const regionCell = await masterSheet.getCellByA1(`E${index + 2}`) // index starts with 2
      regionCell.value = element.region
      const deviceCell = await masterSheet.getCellByA1(`F${index + 2}`) // index starts with 2
      deviceCell.value = element.devices.map((device) => device.model).join(',')
    }

    await masterSheet.saveUpdatedCells()
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${uniqueEmails.length} rows.`
}

const generatePurchaseClicksReport = async (doc: GoogleSpreadsheet, region: string) => {
  const sheet = doc.sheetsByIndex[5]
  const totalSheetRows = sheet.rowCount
  await sheet.loadCells('D1:D1')
  const lastRow = await sheet.getCellByA1('D1')
  const offset = (lastRow.value ?? 0) as number

  const purchaseClicks = await PurchaseClickTracker.query()
    .where('clicks', '>=', 0)
    .whereHas('story', (query) => query.where('region', 'LIKE', `%${region}%`))
    .preload('story')
    .preload('user')
    .orderBy('story_id', 'asc')
    .offset(offset)

  const finalTotal = offset + 1 + purchaseClicks.length
  if (finalTotal > totalSheetRows) {
    const missingRows = finalTotal - totalSheetRows
    const nextIndex = totalSheetRows
    const lastIndex = totalSheetRows + missingRows
    console.log('Append new rows', missingRows)
    await sheet.insertDimension('ROWS', { startIndex: nextIndex, endIndex: lastIndex })
  }

  await sheet.loadCells(`A${offset + 2}:D${purchaseClicks.length + offset + 1}`)

  try {
    for (let index = 0; index < purchaseClicks.length; index++) {
      const element = purchaseClicks[index]
      const currentIndex = offset + index + 2 // index starts with 2

      const userCell = await sheet.getCellByA1(`A${currentIndex}`)
      const storyCell = await sheet.getCellByA1(`B${currentIndex}`)
      const dateCell = await sheet.getCellByA1(`C${currentIndex}`)

      userCell.value = element.user.email
      storyCell.value = element.story.title
      dateCell.value = element.createdAt.toFormat('dd/MM/yyyy HH:mm:ss')
    }

    await sheet.saveUpdatedCells()
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${purchaseClicks.length} rows.`
}

const generateRedemptionReport = async (doc: GoogleSpreadsheet, region: string) => {
  const sheet = doc.sheetsByIndex[6]
  const totalSheetRows = sheet.rowCount
  await sheet.loadCells('E1:E1')
  const lastRow = await sheet.getCellByA1('E1')
  const offset = (lastRow.value ?? 0) as number
  // console.log('offset: ', offset)

  const storyOrders = await StoryOrder.query()
    .whereHas('user', (query) => query.where('region', region))
    .preload('user')
    .preload('voucher')
    .orderBy('id', 'asc')
    .offset(offset)

  // console.log('length: ', storyOrders.length)

  const finalTotal = offset + 1 + storyOrders.length
  if (finalTotal > totalSheetRows) {
    const missingRows = finalTotal - totalSheetRows
    const nextIndex = totalSheetRows
    const lastIndex = totalSheetRows + missingRows
    console.log('Append new rows', missingRows)
    await sheet.insertDimension('ROWS', { startIndex: nextIndex, endIndex: lastIndex })
  }

  await sheet.loadCells(`A${offset + 2}:D${storyOrders.length + offset + 1}`)

  try {
    for (let index = 0; index < storyOrders.length; index++) {
      const element = storyOrders[index]
      const currentIndex = offset + index + 2 // index starts with 2

      const userCell = await sheet.getCellByA1(`A${currentIndex}`)
      const voucherCodeCell = await sheet.getCellByA1(`B${currentIndex}`)
      const paidCell = await sheet.getCellByA1(`C${currentIndex}`)
      const dateCell = await sheet.getCellByA1(`D${currentIndex}`)

      userCell.value = element.user.email
      voucherCodeCell.value = element.voucher?.voucherCode
      paidCell.value = element.finalAmount
      dateCell.value = element.createdAt.toFormat('dd/MM/yyyy HH:mm:ss')
    }

    await sheet.saveUpdatedCells()
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${storyOrders.length} rows.`
}

const generateUserCreditsReport = async (doc: GoogleSpreadsheet, region: string) => {
  const creditSheet = doc.sheetsByIndex[7]

  let regions: string[] = []
  if (region == 'sg') {
    regions = ['sg', 'ww']
  } else {
    regions = [region]
  }

  const users = await User.query()
    .whereIn('region', regions)
    .preload('transactions', query => {
      query.orderBy('id', 'desc')
    })
    .withCount('transactions', (query) => {
      query.sum('amount').as('total')
    })
    .orderBy('id', 'asc')

  await creditSheet.loadCells(`A2:C${users.length + 1}`)

  try {
    for (let index = 0; index < users.length; index++) {
      const element = users[index]
      const userCell = await creditSheet.getCellByA1(`A${index + 2}`) // index starts with 2
      userCell.value = element.email
      const creditCell = await creditSheet.getCellByA1(`B${index + 2}`) // index starts with 2
      creditCell.value = element.$extras.total
      const dateCell = await creditSheet.getCellByA1(`C${index + 2}`) // index starts with 2
      dateCell.value =
        element.transactions.length > 0
          ? element.transactions[0].createdAt
              .setZone('Asia/Kuala_Lumpur')
              .toFormat('yyyy-MM-dd HH:mm:ss')
          : ''
    }

    await creditSheet.saveUpdatedCells()
  } catch (error) {
    throw new Error(error)
  }

  return `Successfully generate ${users.length} rows.`
}

export default class ReportsController {
  public async create({ response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      region: schema.string(),
      report_type: schema.enum([
        'user_masterlist',
        'user_logins',
        'recordings',
        'purchase_clicks',
        'redemption',
        'credits_purchased',
      ]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const region = validationData.region
    let sheetId = ''

    if (region == 'sg') {
      sheetId = '1k8YneBQ-8Lbx0xHX6kSI7z4BzF7dxOWTpjAh1UUFiRA'
    } else if (region == 'tw') {
      sheetId = '1-kyUVowtrwWDJfQSPTsWv_k145cYss4DKM6kscc4tbU'
    } else if (region == 'ww') {
      sheetId = '1EOsoXNFURuwMAzHMsoGoaQ2ldQRagcCDWfS7PVIkABo'
    }

    // Initialize the sheet - doc ID is the long id in the sheets URL
    const doc = new GoogleSpreadsheet(sheetId)

    // Initialize Auth - see https://theoephraim.github.io/node-google-spreadsheet/#/getting-started/authentication
    await doc.useServiceAccountAuth({
      // env var values are copied from service account credentials generated by google
      // see "Authentication" section in docs for more info
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL as string,
      private_key: process.env.GOOGLE_PRIVATE_KEY as string,
    })

    await doc.loadInfo() // loads document properties and worksheets

    let message: string | null = null
    switch (validationData.report_type) {
      case 'user_masterlist':
        message = await generateMasterReport(doc, region)
        break
      case 'user_logins':
        message = await generateLoginActivitiesReport(doc, region)
        break
      case 'recordings':
        message = await generateRecordingsReport(doc, region)
        break
      case 'purchase_clicks':
        message = await generatePurchaseClicksReport(doc, region)
        break
      case 'redemption':
        message = await generateRedemptionReport(doc, region)
        break
      case 'credits_purchased':
        message = await generateUserCreditsReport(doc, region)
        break
      default:
        break
    }

    return response.status(200).send({ success: true, ...(message && { message }) })
  }

  // Excel
  public async generatePackCodesReport({ response }: HttpContextContract) {
    const packCodes = await Database.from('pack_codes AS a')
      .select(['b.title', 'b.language', 'b.region', 'a.pack_code', 'a.created_at', 'c.email'])
      .leftJoin('packs AS b', 'a.pack_id', 'b.id')
      .leftJoin('users as c', 'a.user_id', 'c.id')
      .orderBy('b.title')
      .orderBy('b.region')
      .orderBy('b.language')
      .orderBy('a.created_at')

    return response.ok(packCodes)
  }

  // Excel
  public async generateStoryRatingsReport({ response }: HttpContextContract) {
    const storyRatings = await Database.from('story_ratings AS a')
      .select([
        'a.rating',
        'a.review',
        'a.reason',
        'a.created_at',
        'b.title',
        'b.region',
        'b.language',
        'c.email',
      ])
      .leftJoin('stories AS b', 'a.story_id', 'b.id')
      .leftJoin('users AS c', 'a.user_id', 'c.id')
      .where('a.status', 'rated')
      .orderBy('b.title')
      .orderBy('b.region')
      .orderBy('b.language')
      .orderBy('a.created_at')

    storyRatings.map((storyRating) => {
      return {
        ...storyRating,
        ...(storyRating.reason?.selected_options && {
          reason: storyRating.reason.selected_options.join(' | '),
        }),
      }
    })

    return response.ok(storyRatings)
  }

  // Excel
  public async generateTransactionsReport({ response }: HttpContextContract) {
    const transactions = await Database.from('transactions AS a')
      .select([
        'a.title',
        'a.description',
        'a.amount',
        'a.remark',
        'a.txn_hash',
        'a.created_at',
        'b.email',
      ])
      .leftJoin('users AS b', 'a.user_id', 'b.id')
      .where('status', 'confirmed')
      .orderBy('b.email')
      .orderBy('a.created_at')

    return response.ok(transactions)
  }
}
