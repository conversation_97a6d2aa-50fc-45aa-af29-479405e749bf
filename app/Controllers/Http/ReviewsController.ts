import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Review from 'App/Models/Review'
import _ from 'lodash'

export default class ReviewsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const reviews = await Review.filter(filters)
      .preload('user')
      .preload('story')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(reviews)
  }

  public async findOne({ params, response }: HttpContextContract) {
    const review = await Review.query().where('id', params.id).firstOrFail()

    return response.status(200).send({ data: review })
  }

  public async create({ auth, request, response }: HttpContextContract) {
    const user = await auth.authenticate()
    const validationSchema = schema.create({
      title: schema.string.optional(),
      content: schema.string.optional(),
      score: schema.number(),
      more_activity: schema.boolean(),
      activity_id: schema.number([rules.exists({ table: 'activities', column: 'id' })]),
      user_id: schema.number.optional([rules.exists({ table: 'users', column: 'id' })]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const { user_id: userId, ...rest } = validationData

    const toCreate = {}

    if (userId) {
      // admin create
      toCreate['userId'] = userId
    } else {
      // user submit
      toCreate['userId'] = user.id
    }

    for (let key of Object.keys(rest)) {
      toCreate[_.camelCase(key)] = rest[key]
    }

    const createReview = await Review.create({
      ...toCreate,
    })

    return response.status(200).send({
      success: true,
      data: createReview,
    })
  }

  public async update({ request, response, params }: HttpContextContract) {
    const validationSchema = schema.create({
      title: schema.string.optional(),
      content: schema.string.optional(),
      score: schema.number.optional(),
      more_activity: schema.boolean.optional(),
      user_id: schema.number([rules.exists({ table: 'users', column: 'id' })]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const review = await Review.findByOrFail('id', params.id)

    for (let key of Object.keys(validationData)) {
      review[_.camelCase(key)] = validationData[key]
    }

    await review.save()

    return response.status(200).send({
      success: true,
      data: review,
    })
  }

  public async delete({ params, response }: HttpContextContract) {
    const review = await Review.findOrFail(params.id)
    await review.delete()

    return response.status(200).send({ success: true })
  }
}
