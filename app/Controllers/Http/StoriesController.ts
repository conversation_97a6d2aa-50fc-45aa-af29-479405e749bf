import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Story, { StoryType } from 'App/Models/Story'
import _ from 'lodash'
import UserPack from 'App/Models/UserPack'
import Pack from 'App/Models/Pack'
import Database from '@ioc:Adonis/Lucid/Database'
import { createMediaConvertJob, uploadToS3BucketWithFileName } from './FilesController'
import User from 'App/Models/User'
import Child from 'App/Models/Child'
import File from 'App/Models/File'
import Subscription from 'App/Models/Subscription'
import StoryOrder from 'App/Models/StoryOrder'

export default class StoriesController {
  public async findAll({ auth, request, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const isCommunity = request.input('is_community', 'false')
    const type = request.input('type', 'community')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    if (!_.keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (_.keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    if (_.keys(filters).includes('exclude_redeemed')) {
      if (filters.exclude_redeemed == 'true') {
        if (user) {
          filters.exclude_redeemed = user.id
        } else {
          delete filters.exclude_redeemed
        }
      } else if (filters.exclude_redeemed == 'false') {
        delete filters.exclude_redeemed
      }
    }

    // if logged in return user-preloaded packs
    // otherwise return all packs
    let stories: Story[]
    if (user == null) {
      if (type == 'community' || isCommunity == 'true') {
        stories = await Story.filter(filters)
          .whereNot('status', 'closed')
          .orderByRaw("FIELD(status, 'active', 'upcoming') asc, id desc")
          .paginate(page, limit)
      } else {
        stories = await Story.filter(filters)
          .whereNot('status', 'closed')
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
      }
      return response.status(200).send(stories)
    }

    if (type == 'community' || isCommunity == 'true') {
      // stories = await Story.filter(filters)
      //   .whereNot('status', 'closed')
      //   .as('a')
      //   .joinRaw(
      //     `LEFT JOIN (SELECT story_id, true AS redeemed FROM story_orders WHERE user_id=${user.id}) AS b ON stories.id = b.story_id`
      //   )
      //   .withCount('storyOrders', (query) => query.where('user_id', user.id).as('redeemed'))
      //   .orderByRaw(
      //     "CASE WHEN redeemed = 1 THEN 2 WHEN status = 'active' THEN 1 ELSE 3 END, id DESC"
      //   )
      //   // .orderByRaw("FIELD(status, 'active') desc, FIELD(redeemed, 1) desc, FIELD(status, 'upcoming') desc, id desc")
      //   .paginate(page, limit)
      stories = await Story.filter(filters)
        .whereNot('status', 'closed')
        .withCount('storyOrders', (query) =>
          query.where('user_id', user.id).where('blocked', false).as('redeemed')
        )
        .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)
    } else {
      stories = await Story.filter(filters)
        .whereNot('status', 'closed')
        .withCount('storyOrders', (query) =>
          query.where('user_id', user.id).where('blocked', false).as('redeemed')
        )
        .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)
    }

    return response.status(200).send(stories)
  }

  public async adminFind({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (_.keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    const stories = await Story.filter(filters)
      .preload('chapters', (query) => {
        query.preload('video')
      })
      .preload('previewVideo')
      .preload('purchaseClickTrackers', (query) => {
        query.preload('user')
      })
      .withCount('purchaseClickTrackers', (query) => {
        query.count('*').as('clicks')
      })
      .preload('tags')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(stories)
  }

  public async findMyStories({ request, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (!_.keys(filters).includes('region')) {
      filters['region'] = user.region
    }

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (_.keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    const stories = await Story.filter(filters)
      .where('is_community', true)
      .whereNull('preschool_id')
      .whereHas('storyOrders', (query) => {
        query.where('user_id', user.id).where('blocked', false)
      })
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(stories)
  }

  public async create({ request, response, auth }: HttpContextContract) {
    await auth.authenticate()
    const validationSchema = schema.create({
      title: schema.string(),
      description: schema.string.optional(),
      language: schema.string(),
      level: schema.number(),
      region: schema.array().members(schema.string()),
      thumbnail: schema.file({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      preview_image: schema.file({
        extnames: ['png', 'jpg', 'jpeg'],
        size: '20mb',
      }),
      preview_video: schema.file({
        size: '400mb',
        extnames: ['mp4'],
      }),
      default_chapter_id: schema.number.optional([
        rules.exists({ table: 'chapters', column: 'id' }),
      ]),
      type: schema.enum(Object.values(StoryType)),
      is_community: schema.boolean.optional(),
      is_exclusive: schema.boolean.optional(),
      word_count: schema.number.optional(),
      price: schema.number(),
      compare_at_price: schema.number(),
      is_featured: schema.boolean(),
      metadata: schema.object.optional().anyMembers(),
      preschool_id: schema.number.optional([
        rules.exists({ column: 'id', table: 'preschools' }),
        rules.requiredWhen('type', '=', 'preschool'),
      ]),
      bundle_id: schema.number.optional([rules.exists({ column: 'id', table: 'bundles' })]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    let thumbnailSrc: string | undefined
    let previewImageSrc: string | undefined
    let previewVideoSrc: string | undefined
    if (validationData.thumbnail) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        '',
        validationData.thumbnail.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      thumbnailSrc = uploadBucket.url
    }

    if (validationData.preview_image) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.preview_image,
        process.env.S3_BUCKET ?? 'hummusedu',
        '',
        validationData.preview_image.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      previewImageSrc = uploadBucket.url
    }

    if (validationData.preview_video) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.preview_video,
        process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
        '',
        validationData.preview_video.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }
      const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
      const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
      // console.log('input: ', inputS3Url)
      // console.log('output: ', outputS3Url)
      const job = await createMediaConvertJob(inputS3Url, outputS3Url)
      if (job.Status !== 'SUBMITTED') {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      previewVideoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
        '.mp4',
        '_1080p.m3u8'
      )}`
    }

    const result = await Database.transaction(async (trx) => {
      let fileId
      if (previewVideoSrc) {
        const chapterVideo = new File()
        chapterVideo.src = previewVideoSrc
        chapterVideo.type = 'video'
        chapterVideo.useTransaction(trx)
        await chapterVideo.save()
        fileId = chapterVideo.id
      }

      const createStory = new Story()
      createStory.level = validationData.level
      createStory.language = validationData.language
      createStory.title = validationData.title
      createStory.description = validationData.description ?? null
      createStory.region = validationData.region
      if (thumbnailSrc) {
        createStory.thumbnailUrl = thumbnailSrc
      }
      if (previewImageSrc) {
        createStory.previewImageUrl = previewImageSrc
      }
      if (fileId) {
        createStory.previewVideoId = fileId
      }
      if (validationData.word_count) {
        createStory.wordCount = validationData.word_count
      }
      createStory.price = validationData.price
      createStory.compareAtPrice = validationData.compare_at_price ?? 0
      createStory.isFeatured = validationData.is_featured
      createStory.metadata = validationData.metadata ?? {}

      switch (validationData.type) {
        case StoryType.COMMUNITY:
          createStory.isCommunity = true
          createStory.type = StoryType.COMMUNITY
          break
        case StoryType.PRESCHOOL:
          createStory.preschoolId = validationData.preschool_id!
          createStory.type = StoryType.PRESCHOOL
          break
        default:
          createStory.type = validationData.type
      }

      createStory.useTransaction(trx)
      await createStory.save()

      return createStory
    })

    return response.status(200).send({ success: true, data: result })
  }

  public async update({ params, response, request, auth }: HttpContextContract) {
    await auth.authenticate()
    const validationSchema = schema.create({
      title: schema.string.optional(),
      description: schema.string.optional(),
      language: schema.string.optional(),
      level: schema.number.optional(),
      region: schema.array().members(schema.string()),
      thumbnail: schema.file.optional(
        {
          extnames: ['png', 'jpg', 'jpeg'],
          size: '20mb',
        },
        [rules.requiredWhen('is_thumbnail_removed', '=', 'true')]
      ),
      default_chapter_id: schema.number.optional([
        rules.exists({ table: 'chapters', column: 'id', where: { story_id: params.id } }),
      ]),
      is_community: schema.boolean.optional(),
      is_thumbnail_removed: schema.boolean.optional(),
      tags: schema.array.optional().members(schema.number([rules.exists({ table: 'tags', column: 'id' })])),
      word_count: schema.number.optional(),
      status: schema.enum(['active', 'upcoming', 'closed']),
      price: schema.number(),
      preview_image: schema.file.optional(
        {
          extnames: ['png', 'jpg', 'jpeg'],
          size: '20mb',
        },
        [rules.requiredWhen('is_preview_image_removed', '=', 'true')]
      ),
      is_preview_image_removed: schema.boolean.optional(),
      preview_video: schema.file.optional(
        {
          size: '400mb',
          extnames: ['mp4'],
        },
        [rules.requiredWhen('is_preview_video_removed', '=', 'true')]
      ),
      is_preview_video_removed: schema.boolean.optional(),
      compare_at_price: schema.number.optional(),
      is_featured: schema.boolean(),
      metadata: schema.object.optional().anyMembers(),
      preschool_id: schema.number.optional([rules.exists({ column: 'id', table: 'preschools' })]),
    })

    const validationData = await request.validate({
      schema: validationSchema,
      messages: {
        required: 'The {{field}} is required',
        requiredWhen: 'The {{field}} is required',
      },
    })

    let thumbnailSrc: string
    let previewImageSrc: string
    let previewVideoSrc: string
    if (validationData.thumbnail) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.thumbnail,
        process.env.S3_BUCKET ?? 'hummusedu',
        '',
        validationData.thumbnail.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      thumbnailSrc = uploadBucket.url
    }

    if (validationData.preview_image) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.preview_image,
        process.env.S3_BUCKET ?? 'hummusedu',
        '',
        validationData.preview_image.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      previewImageSrc = uploadBucket.url
    }

    if (validationData.preview_video) {
      const uploadBucket = await uploadToS3BucketWithFileName(
        validationData.preview_video,
        process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
        '',
        validationData.preview_video.clientName ?? undefined
      )

      // console.log(uploadBucket)
      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }
      const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
      const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
      // console.log('input: ', inputS3Url)
      // console.log('output: ', outputS3Url)
      const job = await createMediaConvertJob(inputS3Url, outputS3Url)
      if (job.Status !== 'SUBMITTED') {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      previewVideoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
        '.mp4',
        '_1080p.m3u8'
      )}`
    }

    const updateStory = await Story.findOrFail(params.id)

    const result = await Database.transaction(async (trx) => {
      let fileId
      if (previewVideoSrc) {
        const chapterVideo = new File()
        chapterVideo.src = previewVideoSrc
        chapterVideo.type = 'video'
        chapterVideo.useTransaction(trx)
        await chapterVideo.save()
        fileId = chapterVideo.id
      }

      if (validationData.title) {
        updateStory.title = validationData.title
      }
      if (validationData.description) {
        updateStory.description = validationData.description
      }
      if (validationData.language) {
        updateStory.language = validationData.language
      }
      if (validationData.level) {
        updateStory.level = validationData.level
      }
      if (validationData.region) {
        updateStory.region = validationData.region
      }
      if (validationData.default_chapter_id) {
        updateStory.defaultChapterId = validationData.default_chapter_id
      } else {
        updateStory.defaultChapterId = null
      }
      if (thumbnailSrc) {
        updateStory.thumbnailUrl = thumbnailSrc
      } else if (validationData.is_thumbnail_removed) {
        updateStory.thumbnailUrl = null
      }
      if (previewImageSrc) {
        updateStory.previewImageUrl = previewImageSrc
      } else if (validationData.is_thumbnail_removed) {
        updateStory.previewImageUrl = null
      }
      if (fileId) {
        updateStory.previewVideoId = fileId
      } else if (validationData.is_preview_video_removed) {
        updateStory.previewVideoId = null
      }
      if (validationData.word_count) {
        updateStory.wordCount = validationData.word_count
      }

      // for switching community to preschool or the other way round
      if (validationData.is_community && validationData.preschool_id) {
        if (!updateStory.preschoolId) {
          updateStory.preschoolId = validationData.preschool_id
          updateStory.isCommunity = false
          updateStory.type = StoryType.PRESCHOOL
        } else if (!updateStory.isCommunity) {
          updateStory.preschoolId = null
          updateStory.isCommunity = true
          updateStory.type = StoryType.COMMUNITY
        }
      }

      await updateStory.related('tags').sync(validationData.tags ?? [])

      updateStory.status = validationData.status
      updateStory.price = validationData.price
      updateStory.compareAtPrice = validationData.compare_at_price ?? 0
      updateStory.isFeatured = validationData.is_featured
      updateStory.metadata = validationData.metadata ?? {}

      updateStory.useTransaction(trx)
      await updateStory.save()
      return updateStory
    })

    return response.status(200).send({ success: true, data: result })
  }

  public async deleteStory({ params, response }: HttpContextContract) {
    const deleteStory = await Story.findOrFail(params.id)
    await deleteStory.delete()

    return response.status(200).send({ success: true })
  }

  public async findStoriesByLevel({ request, response }: HttpContextContract) {
    const validateDataSchema = schema.create({
      level: schema.number(),
    })
    const validateData = await request.validate({
      schema: validateDataSchema,
    })
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    const stories = await Story.filter(filters)
      .where('level', validateData.level)
      .preload('chapters')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(stories)
  }

  public async findStoriesByPackAndLevel({ request, response, params }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    const stories = await Story.filter(filters)
      .where('level', params.level)
      .where('pack_id', params.pack_id)
      .preload('chapters')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(stories)
  }

  public async findStoriesByPreschool({
    request,
    response,
    params: { preschool_id },
  }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

    if (_.keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    const stories = await Story.filter(filters)
      .where('preschool_id', preschool_id)
      .preload('chapters')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(stories)
  }

  public async findOne({ auth, response, params }: HttpContextContract) {
    console.log('StoriesController findOne: ', params)
    const user = await User.find(auth.user?.id ?? 31)

    if (user == null) {
      const story = await Story.query()
        .where('id', params.id)
        .preload('previewVideo')
        .preload('defaultChapter')
        .preload('chapters')
        .first()
      return response.status(200).send({ data: story })
    }

    // check if the story is free in any plan
    const isFreeInAnyPlan = await Story.query()
      .where('id', params.id)
      .whereHas('plans', (planQuery) => {
        planQuery.whereHas('planStories', (planStoryQuery) => {
          planStoryQuery.where('story_id', params.id).where('is_free', true)
        })
      })
      .first()

    if (isFreeInAnyPlan) {
      // if the story is free, create a StoryOrder if the user doesn't have one
      await StoryOrder.firstOrCreate(
        {
          storyId: params.id,
          userId: user.id,
        },
        {
          storyId: params.id,
          userId: user.id,
        }
      )
    } else {
      // check if user has 'active' subscriptions that include this story
      const subscriptionWithStory = await Subscription.query()
        .where('user_id', user.id)
        .whereIn('status', ['trailing', 'active'])
        .whereNull('end_date')
        .whereHas('plan', (query) =>
          query.whereHas('planStories', (query) => query.where('story_id', params.id))
        )
        .first()

      // make sure we create a story order if subscription is active
      if (subscriptionWithStory) {
        await StoryOrder.firstOrCreate(
          {
            storyId: params.id,
            userId: user.id,
          },
          {
            storyId: params.id,
            userId: user.id,
          }
        )
      }
    }

    const story = await Story.query()
      .where('id', params.id)
      .preload('previewVideo')
      .preload('defaultChapter', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .preload('chapters', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .withCount('storyOrders', (query) =>
        query.where('user_id', user.id).where('blocked', false).as('redeemed')
      )
      .first()

    return response.send({
      data: story,
    })
  }

  public async adminFindOne({ params, response }: HttpContextContract) {
    const story = await Story.query()
      .where('id', params.id)
      .preload('chapters', (query) => {
        query.preload('video')
      })
      .firstOrFail()

    const defaultChapter = story.chapters.find((chapter) => chapter.id === story.defaultChapterId)
    if (defaultChapter) {
      story.chapters.splice(story.chapters.indexOf(defaultChapter), 1)
      story.chapters.unshift(defaultChapter)
    }

    return response.status(200).send({ data: story })
  }

  public async findStory({ params, response, auth }: HttpContextContract) {
    const user = await User.findOrFail(auth.user?.id)

    const story = await Story.query()
      .where('id', params.id)
      .whereHas('storyOrders', (query) => query.where('blocked', false).where('user_id', user.id))
      .preload('chapters')
      .preload('previewVideo')
      .first()

    return response.status(200).send(story)
  }

  // no need user
  public async instantFindStory({ params, response }: HttpContextContract) {
    const story = await Story.query()
      .where('id', params.id)
      .preload('previewVideo')
      .preload('defaultChapter', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .preload('chapters', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .first()

    return response.status(200).send(story)
  }

  public async findStoryByHandle({ auth, response, params }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    if (user == null) {
      const story = await Story.query()
        .where('handle', params.handle)
        .preload('previewVideo')
        .preload('defaultChapter')
        .preload('chapters')
        .first()
      return response.status(200).send({ data: story })
    }

    const story = await Story.query()
      .where('handle', params.handle)
      .preload('previewVideo')
      .preload('defaultChapter', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .preload('chapters', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .withCount('storyOrders', (query) =>
        query.where('user_id', user.id).where('blocked', false).as('redeemed')
      )
      .first()

    return response.send({
      data: story,
    })
  }

  public async findStoryByQrCode({
    auth,
    // request,
    response,
    params,
  }: HttpContextContract) {
    // need to check if user has access to this story
    const user = await auth.authenticate()
    // const level = request.input('level')

    const pack = await Pack.query()
      .whereHas('stories', (query) => query.where('qr_code', params.qrCode))
      .preload('stories')
      .firstOrFail()

    // TODO for community
    const userPack = await UserPack.query()
      .where('user_id', user.id)
      .where('pack_id', pack.id)
      .first()

    if (!userPack) {
      // not redeemed
      return response.status(400).send({ data: null, message: 'not.redeemed' })
    }

    const activeChild = await Child.find(user.activeChildId)

    // need to check if previous level has finished
    const previousLevel = userPack.currentLevel > 1 ? userPack.currentLevel - 1 : 1

    if (userPack.currentLevel > 1) {
      const storyStatus = userPack.storyStatuses

      if (storyStatus == null) {
        return response.status(400).send({ data: null, message: 'level.not.ready' })
      }

      const packStories = pack.stories
      const storiesForPreviousLevel = packStories.filter((story) => {
        return story.level == previousLevel
      })
      const storyIds = storiesForPreviousLevel.map((story) => {
        return story.id
      })

      const hasCompletedPreviousLevel = storyIds.every((id) => {
        let index = -1
        if (activeChild == null) {
          index = storyStatus.findIndex((status) => {
            return status.story_id == id
          })
        } else {
          index = storyStatus.findIndex((status) => {
            return status.story_id == id && status.child_id == activeChild.id
          })
        }

        return index >= 0
      })

      if (!hasCompletedPreviousLevel) {
        return response.status(400).send({ data: null, message: 'level.not.ready' })
      }
    }

    const story = await Story.query()
      .where('qr_code', params.qrCode)
      .whereHas('pack', (query) => {
        query.whereHas('userPacks', (query) => {
          query.where('user_packs.id', userPack.id)
        })
      })
      .where('level', userPack.currentLevel)
      .preload('pack')
      .preload('chapters', (query) => {
        query.preload('video')
      })
      .firstOrFail()

    const defaultChapter = story.chapters.find((chapter) => chapter.id === story.defaultChapterId)
    if (defaultChapter) {
      story.chapters.splice(story.chapters.indexOf(defaultChapter), 1)
      story.chapters.unshift(defaultChapter)
    }

    return response.status(200).send({ data: story })
  }

  // sorting stories for preschool and community section, preschool stories must not be exist in community section
  public async communityStoriesSorting({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        sorting_list: schema.array().members(schema.number()),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const sortingList = validationData.sorting_list

      await Database.transaction(async (trx) => {
        for (let i = 0; i < sortingList.length; i++) {
          const storyId = sortingList[i]
          const ordering = i + 1
          const findStory = await Story.findOrFail(storyId)
          if (findStory.ordering == ordering) continue

          findStory.ordering = ordering
          findStory.useTransaction(trx)
          await findStory.save()
        }
      })

      return response.status(200).send({
        success: true,
        message: 'Sort successfully',
      })
    } catch (error) {
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  // sorting stories for bundle only
  // public async bundleStoriesSorting({ request, response }: HttpContextContract) {
  //   try {
  //     const validationSchema = schema.create({
  //       bundle_id: schema.number([rules.exists({ table: 'bundles', column: 'id' })]),
  //       sorting_list: schema.array().members(
  //         schema.number([
  //           rules.exists({
  //             table: 'stories',
  //             column: 'id',
  //             where: { preschool_id: request.body().preschool_id },
  //           }),
  //         ])
  //       ),
  //     })

  //     const validationData = await request.validate({ schema: validationSchema })
  //     const sortingList = validationData.sorting_list

  //     await Database.transaction(async (trx) => {
  //       for (let i = 0; i < sortingList.length; i++) {
  //         const storyId = sortingList[i]
  //         const ordering = i + 1
  //         const findStory = await Story.findOrFail(storyId)
  //         if (findStory.ordering == ordering) continue

  //         findStory.bundleOrdering = ordering
  //         findStory.useTransaction(trx)
  //         await findStory.save()
  //       }
  //     })

  //     return response.status(200).send({
  //       success: true,
  //       message: 'Sort successfully',
  //     })
  //   } catch (error) {
  //     return response.status(400).send({
  //       success: false,
  //       error,
  //     })
  //   }
  // }
}
