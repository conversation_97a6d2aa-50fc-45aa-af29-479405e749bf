import { schema, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import _ from 'lodash'
import Chat from 'App/Models/Chat'
import ChatHistory from 'App/Models/ChatHistory'
import Event from '@ioc:Adonis/Core/Event'
import { DateTime } from 'luxon'

export default class UserChatsController {
  public async find({ response, request, auth }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')
    const result = await Chat.query()
      .where('user_id', auth.user!.id)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(result)
  }

  public async findOne({ response, params: { id }, auth }: HttpContextContract) {
    const result = await Chat.query()
      .where('id', id)
      .andWhere('user_id', auth.user!.id)
      .preload('user')
      .first()
    if (!result) {
      return response.notFound()
    }
    return response.ok(result)
  }

  public async create({ response, request, auth }: HttpContextContract) {
    const postSchema = schema.create({
      content: schema.string(),
      user_id: schema.number([
        rules.exists({
          table: 'users',
          column: 'id',
        }),
      ]),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const chat = await Chat.create({ userId: payload.user_id })
      const history = new ChatHistory()

      history.merge({
        userId: auth.user!.id,
        content: payload.content,
        chatId: chat.id,
        seen: DateTime.now(),
        createdAt: chat.createdAt,
      })
      await history.save()
      Event.emit('chat:create', chat.id)
      return response.created({ data: chat, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async createHistory({ request, response, params: { id }, auth }: HttpContextContract) {
    const postSchema = schema.create({
      content: schema.string(),
    })

    try {
      const chat = await Chat.query().where('id', id).andWhere('user_id', auth.user!.id).first()
      if (!chat) {
        return response.notFound()
      }

      const payload = await request.validate({ schema: postSchema })

      const history = await ChatHistory.create({
        ...payload,
        userId: auth.user!.id,
        chatId: chat.id,
        seen: DateTime.local(), // user's message set to seen to avoid counted as unread
      })

      Event.emit('history:create', history.id)
      return response.created({ data: history, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async findHistories({ request, response, params: { id }, auth }: HttpContextContract) {
    const chat = await Chat.query().where('id', id).andWhere('user_id', auth.user!.id).first()
    if (!chat) {
      return response.notFound()
    }

    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')
    const result = await ChatHistory.query()
      .preload('user')
      .where('chat_id', id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    Event.emit('chat:seen', {
      historyIds: result.map((item) => item.id),
      chatId: chat.id,
    })
    return response.ok(result)
  }
}
