// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import { schema, rules } from '@ioc:Adonis/Core/Validator'
// import Database from '@ioc:Adonis/Lucid/Database'
// import User from 'App/Models/User'
// import UserGroup from 'App/Models/UserGroup'
// import _ from 'lodash'

// export default class UserGroupsController {
//   public async find({ response, request }: HttpContextContract) {
//     const page = request.input('page', 1)
//     const limit = request.input('limit', 20)
//     const sort = request.input('sort', 'id:desc').split(':')
//     const filters = _.omit(request.all(), ['page', 'limit', 'sort'])

//     const userGroups = await UserGroup.filter(filters)
//       .withCount('users')
//       .as('users_count')
//       .withCount('plans')
//       .as('plans_count')
//       .orderBy(sort[0], sort[1])
//       .paginate(page, limit)

//     return response.status(200).send(userGroups)
//   }

//   public async findOne({ response, params }: HttpContextContract) {
//     const userGroup = await UserGroup.query()
//       .where('id', params.id)
//       .preload('users')
//       .preload('plans', (query) => query.pivotColumns(['id']))
//       .firstOrFail()

//     return response.status(200).send({
//       data: userGroup,
//     })
//   }

//   public async findMyUserGroup({ response, auth }: HttpContextContract) {
//     const user = await User.findOrFail(auth?.user?.id)
//     const userGroup = await user.related('userGroup').query().preload('plans').firstOrFail()

//     return response.status(200).send({
//       data: userGroup,
//     })
//   }

//   public async create({ request, response }: HttpContextContract) {
//     const validationSchema = schema.create({
//       name: schema.string({ trim: true }),
//       url: schema.string({ trim: true }),
//       is_default: schema.boolean.optional(),
//       pack_id: schema.number([rules.exists({ table: 'packs', column: 'id' })]),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     if (validationData.is_default === true) {
//       const defaultUserGroup = await UserGroup.query().where('is_default', true).first()
//       if (defaultUserGroup) {
//         defaultUserGroup.isDefault = false
//         defaultUserGroup.save()
//       }
//     }

//     const createUserGroup = await UserGroup.create({
//       ...validationData,
//     })

//     return response.status(200).send({
//       success: true,
//       data: createUserGroup,
//     })
//   }

//   public async update({ request, response, params }: HttpContextContract) {
//     const validationSchema = schema.create({
//       name: schema.string.optional({ trim: true }),
//       url: schema.string.optional({ trim: true }),
//       is_default: schema.boolean.optional(),
//       pack_id: schema.number([rules.exists({ table: 'packs', column: 'id' })]),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     if (validationData.is_default === true) {
//       const defaultUserGroup = await UserGroup.query().where('is_default', true).first()
//       if (defaultUserGroup) {
//         defaultUserGroup.isDefault = false
//         defaultUserGroup.save()
//       }
//     }

//     const userGroup = await UserGroup.findByOrFail('id', params.id)
//     for (let key of Object.keys(validationData)) {
//       userGroup[_.camelCase(key)] = validationData[key]
//     }
//     await userGroup.save()

//     return response.status(200).send({
//       success: true,
//       data: userGroup,
//     })
//   }

//   public async delete({ response, params }: HttpContextContract) {
//     const userGroup = await UserGroup.query().where('id', params.id).firstOrFail()
//     await userGroup.delete()

//     return response.status(200).send({
//       success: true,
//     })
//   }

//   public async addUser({ request, response, params }: HttpContextContract) {
//     const validationSchema = schema.create({
//       user_id: schema.number([rules.exists({ table: 'users', column: 'id' })]),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     const findUserGroup = await UserGroup.findOrFail(params.id)
//     const findUser = await User.findOrFail(validationData.user_id)

//     findUser.userGroupId = findUserGroup.id
//     await findUser.save()

//     return response.status(200).send({ success: true })
//   }

//   public async deleteUser({ response, params }: HttpContextContract) {
//     const findUser = await User.findOrFail(params.user_id)

//     findUser.userGroupId = null
//     await findUser.save()

//     return response.status(200).send({ success: true })
//   }

//   public async addPlan({ request, response, params }: HttpContextContract) {
//     const validationSchema = schema.create({
//       plan_id: schema.number([rules.exists({ table: 'plans', column: 'id' })]),
//     })

//     const validationData = await request.validate({
//       schema: validationSchema,
//     })

//     const findUserGroup = await UserGroup.findOrFail(params.id)

//     // prevent duplicated activity
//     const findPlan = await findUserGroup
//       .related('plans')
//       .query()
//       .where('plan_id', validationData.plan_id)
//       .first()

//     if (!findPlan) {
//       await findUserGroup.related('plans').attach([validationData.plan_id])
//     }

//     return response.status(200).send({ success: true })
//   }

//   public async deletePlan({ response, params }: HttpContextContract) {
//     await Database.from('plan_user_groups')
//       .where('user_group_id', params.id)
//       .where('plan_id', params.plan_id)
//       .delete()

//     return response.status(200).send({ success: true })
//   }
// }
