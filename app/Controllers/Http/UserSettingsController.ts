import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import UserSetting from 'App/Models/UserSetting'

export default class UserSettingsController {
  public async findUserSetting({ response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()

      let userSettings
      await Database.transaction(async (trx) => {
        userSettings = await UserSetting.firstOrCreate(
          { userId: user.id },
          {
            metadata: {
              repeat_animation: 0,
            },
          },
          { client: trx }
        )
      })

      return response.ok({ data: userSettings })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async updateUserSetting({ request, response, auth, params: { id } }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const validationSchema = schema.create({
        metadata: schema.object().members({
          repeat_animation: schema.number([rules.range(0, 1)]),
        }),
      })
      const validationData = await request.validate({ schema: validationSchema })

      const userSetting = await UserSetting.findOrFail(id)
      if (userSetting.userId !== user.id) {
        return response.badRequest({ success: false, message: 'Invalid access' })
      }
      await Database.transaction(async (trx) => {
        userSetting.merge({ ...validationData })
        userSetting.useTransaction(trx)
        await userSetting.save()
      })

      return response.ok({ success: true })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
