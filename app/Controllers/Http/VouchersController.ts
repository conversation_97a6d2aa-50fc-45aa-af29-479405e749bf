import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Story from 'App/Models/Story'
import Voucher from 'App/Models/Voucher'
import _ from 'lodash'
import { DateTime } from 'luxon'
import Randomstring from 'randomstring'
import BigNumber from 'bignumber.js'
import Preschool from 'App/Models/Preschool'

export default class VouchersController {
  public async findAll({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const voucherCodes = await Voucher.filter(filters)
      .preload('story')
      .preload('preschool')
      .preload('storyOrders')
      .withCount('storyOrders', (query) => query.countDistinct('user_id').as('total_of_used'))
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(voucherCodes)
  }

  public async create({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        code: schema.string.optional(),
        description: schema.string.optional(),
        discount: schema.number(),
        story_id: schema.number.optional([
          rules.exists({ table: 'stories', column: 'id' }),
          rules.requiredIfNotExists('preschool_id'),
        ]),
        preschool_id: schema.number.optional([
          rules.exists({ table: 'preschools', column: 'id' }),
          rules.requiredIfNotExists('story_id'),
        ]),
        max_of_used: schema.number.optional(),
        expired_at: schema.date.optional(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      if (validationData.code) {
        const repeatedCode = await Voucher.findBy('voucher_code', validationData.code)
        if (repeatedCode) {
          return response.status(400).send({ success: false, message: 'The code already exists.' })
        }
      }

      let uid: string = validationData.code ?? ''
      let isUnique = true
      while (uid == '' || !isUnique) {
        uid = Randomstring.generate(6)
        const repeatedCode = await Voucher.findBy('voucher_code', uid)
        if (repeatedCode) {
          isUnique = false
        } else isUnique = true
      }

      const result = await Database.transaction(async (trx) => {
        const newVoucher = new Voucher()
        newVoucher.voucherCode = uid
        if (validationData.description) {
          newVoucher.description = validationData.description
        }
        if (validationData.story_id) {
          newVoucher.storyId = validationData.story_id
        } else if (validationData.preschool_id) {
          newVoucher.preschoolId = validationData.preschool_id
        }

        if (validationData.max_of_used) {
          newVoucher.maxOfUsed = validationData.max_of_used
        }

        if (validationData.expired_at) {
          let utc = validationData.expired_at
          let gmt8 = utc.setZone('Asia/Kuala_lumpur').set({ hour: 23, minute: 59, second: 59 })
          newVoucher.expiredAt = gmt8.toLocal()
        }
        newVoucher.discount = validationData.discount

        newVoucher.useTransaction(trx)
        await newVoucher.save()

        return newVoucher
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      console.log(error)
    }
  }

  public async verify({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        code: schema.string(),
        // story_id: schema.number([rules.exists({ table: 'stories', column: 'id' })]),
        story_id: schema.string.optional([rules.exists({ table: 'stories', column: 'id' })]),
        preschool_id: schema.string.optional([rules.exists({ table: 'preschools', column: 'id' })]),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const findStory = await Story.find(validationData.story_id ?? 0)
      const findPreschool = await Preschool.find(validationData.preschool_id ?? 0)

      const voucher = await Voucher.query()
        .where('voucher_code', validationData.code)
        .where((query) => {
          query
            .whereNull('expired_at')
            .orWhere('expired_at', '>=', DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'))
        })
        .first()

      if (
        !voucher ||
        (voucher.storyId != findStory?.id && voucher.preschoolId != findPreschool?.id)
      ) {
        return response.status(400).send({
          success: false,
          message: 'Invalid code.',
        })
      }

      if (findStory && (!findStory?.price || (findStory?.price ?? 0) <= 0)) {
        return response.status(400).send({ success: false, message: 'Invalid price' })
      }
      // const price = findStory?.price ?? 0
      // const discount = voucher.discount
      // const finalPrice = new BigNumber(price!)
      //   .multipliedBy(1 - discount)
      //   .decimalPlaces(2)
      //   .toNumber()

      return response.status(200).send({
        success: true,
        data: {
          ...(findStory && {
            story_id: findStory.id,
            price: findStory.price,
            discount: voucher.discount,
            final_price: new BigNumber(findStory.price)
              .multipliedBy(1 - voucher.discount)
              .decimalPlaces(2)
              .toNumber(),
          }),
          ...(findPreschool && { preschool_id: findPreschool.id }),
          voucher_id: voucher.id,
        },
      })
    } catch (error) {
      console.log(error)
    }
  }
}
