import Bull from '@ioc:Bull/Queue'
import { Job, Queue, Worker } from 'bullmq'

class PlanQueue {
  private queue: Queue
  private _worker: Worker | null = null

  constructor() {
    this.queue = Bull.queue('plan-events')
  }

  public async add(jobName: string, data: any) {
    await this.queue.add(jobName, data)
  }

  public worker(processor: (job: Job) => Promise<any>) {
    if (!this._worker) {
      this._worker = Bull.worker('plan-events', processor)
    }
    return this._worker
  }
}

export const planQueue = new PlanQueue()

export const PlanJobKeys = {
  PRODUCT_CREATED: 'plan:product:created',
  PRICE_CREATED: 'plan:price:created',
  PRODUCT_UPDATED: 'plan:product:updated',
  PRICE_UPDATED: 'plan:price:updated',
  PRODUCT_DELETED: 'plan:product:deleted',
  PRICE_DELETED: 'plan:price:deleted',
  CUSTOMER_CREATED: 'plan:customer:created',
  CUSTOMER_UPDATED: 'plan:customer:updated',
  SUBSCRIPTION_CREATED: 'plan:subscription:created',
  SUBSCRIPTION_UPDATED: 'plan:subscription:updated',
  SUBSCRIPTION_DELETED: 'plan:subscription:deleted',
  INVOICE_PAID: 'plan:invoice:paid',
  INVOICE_FAILED: 'plan:invoice:failed',
}
