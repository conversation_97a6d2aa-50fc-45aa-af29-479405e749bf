import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import AdminFilter from './Filters/AdminFilter'

export default class Admin extends compose(BaseModel, Filterable) {
  public static $filter = () => AdminFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @belongsTo(() => User, {})
  public user: BelongsTo<typeof User>

  @column()
  public type: number

  //crud
  //0-r,1-rc,2-rcu,3-rcud
  @column()
  public accessKyc: number

  @column()
  public accessFunding: number

  @column()
  public accessUser: number

  @column()
  public accessReport: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
