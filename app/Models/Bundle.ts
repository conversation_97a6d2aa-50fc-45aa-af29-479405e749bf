import { DateTime } from 'luxon'
import { BaseModel, ManyToMany, column, computed, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'
import { BigNumber } from 'bignumber.js'

export default class Bundle extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public description: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public region: string | Array<string>

  @column()
  public language: string

  @column({ consume: (value) => Number(value) })
  public discount: number

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Story, {
    pivotTable: 'bundle_stories',
    pivotForeignKey: 'bundle_id',
    pivotRelatedForeignKey: 'story_id',
  })
  public stories: ManyToMany<typeof Story>

  @computed()
  public get price() {
    let price = 0
    if ((this.stories?.length ?? 0) > 0) {
      this.stories.forEach((story) => {
        if (!story.hasRedeemed) {
          price += story.price ?? 0
        }
      })

      price = price * (1 - (this.discount ?? 0))
    }

    return new BigNumber(price).decimalPlaces(2).toNumber()
  }
}
