import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Bundle from './Bundle'
import Story from './Story'

export default class BundleStory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public bundleId: number

  @column()
  public storyId: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Bundle)
  public bundle: BelongsTo<typeof Bundle>

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>
}
