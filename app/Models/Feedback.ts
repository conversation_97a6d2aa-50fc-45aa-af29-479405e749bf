import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import FeedbackFilter from './Filters/FeedbackFilter'

export default class Feedback extends compose(BaseModel, Filterable) {
  public static $filter = () => FeedbackFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public subject: string

  @column()
  public content: string

  // to ensure correct user in reply form
  @column()
  public token: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isClosed: boolean

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
