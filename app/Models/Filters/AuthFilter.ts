import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Auth from 'App/Models/Auth'

export default class AuthFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Auth, Auth>

  public email(value: any): void {
    this.$query.where('email', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    this.$query.where('status', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }
}
