import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Banner from 'App/Models/Banner'

export default class BannerFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Banner, Banner>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public region(value: any): void {
    this.$query.where('region', value)
  }
}
