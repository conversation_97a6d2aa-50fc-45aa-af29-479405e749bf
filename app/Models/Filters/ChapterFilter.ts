import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Chapter from 'App/Models/Chapter'

export default class ChapterFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Chapter, Chapter>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public story(value: any): void {
    this.$query.where('story_id', value)
  }

  public preschool(value: any): void {
    this.$query.whereHas('story', (query) => query.where('preschool_id', value))
  }

  public plan(value: any): void {
    this.$query.whereHas('story', (query) => query.where('plan_id', value))
  }

  public bundle(value: any): void {
    this.$query.whereHas('story', (query) =>
      query.whereHas('bundleStory', (query) => query.where('bundle_id', value))
    )
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
