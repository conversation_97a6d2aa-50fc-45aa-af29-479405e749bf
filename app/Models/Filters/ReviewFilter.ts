import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Review from 'App/Models/Review'

export default class ReviewFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Review, Review>

  public email(value: any): void {
    this.$query.whereHas('user', (query) => query.where('email', value))
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
