import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import User from 'App/Models/User'

export default class UserFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof User, User>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public email(value: any): void {
    this.$query.where('email', 'LIKE', `%${value}%`)
  }

  public name(value: any): void {
    this.$query.where('name', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`).orWhere('region', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }

  public subscribePreschool(value: any): void {
    this.$query.whereHas('storyOrders', (query) => {
      query.where('blocked', false).whereHas('story', (query) => query.where('preschool_id', value))
    })
  }

  public subscribePlan(value: any): void {
    this.$query.whereHas('subscriptions', (query) => {
      query.where('blocked', false).where('plan_id', value)
    })
  }
}
