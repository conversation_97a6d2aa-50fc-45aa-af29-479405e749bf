import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import UserGroup from 'App/Models/UserGroup'

export default class UserGroupFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof UserGroup, UserGroup>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }
}
