import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Version from 'App/Models/Version'

export default class VersionFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Version, Version>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public name(value: any): void {
    this.$query.where('name', 'LIKE', `%${value}%`)
  }

  public platform(value: any): void {
    this.$query.where('platform', value)
  }
}
