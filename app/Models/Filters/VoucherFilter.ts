import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Voucher from 'App/Models/Voucher'

export default class VoucherFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Voucher, Voucher>

  public voucherCode(value: any): void {
    this.$query.whereRaw('LOWER(voucher_code)', 'LIKE', `%${value.toLowerCase()}%`)
  }

  public story(value: any): void {
    this.$query.where('story_id', Number(value))
  }

  public title(value: any): void {
    const keyword = String(value || '').toLowerCase()
    if (!keyword) return
    this.$query.whereHas('story', (storyQuery) => {
      storyQuery.whereHas('pack', (packQuery) => {
        packQuery.whereRaw('LOWER(title) LIKE ?', [`%${keyword}%`])
      })
    })
  }
}
