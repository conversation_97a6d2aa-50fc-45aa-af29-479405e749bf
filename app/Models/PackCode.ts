import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import UserGroup from './UserGroup'
import Pack from './Pack'
import PackCodeFilter from './Filters/PackCodeFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'

export default class PackCode extends compose(BaseModel, Filterable) {
  public static $filter = () => PackCodeFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public packId: number

  @belongsTo(() => Pack)
  public pack: BelongsTo<typeof Pack>

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public userGroupId: number

  @belongsTo(() => UserGroup)
  public userGroup: BelongsTo<typeof UserGroup>

  @column()
  public packCode: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
