import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import File from './File'
import Pack from './Pack'

export type RiveInput = {
  key: string
  type: string
  story_id?: number
  value?: boolean
}
export default class PackLevel extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public level: number

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  public inputs: Array<RiveInput>
  // db: [({ key: 'outdoor', type: 'boolean', story_id: 1 }, { key: 'bedroom', type: 'boolean', story_id: 2 })]
  // computed: [{ key: 'outdoor', type: 'boolean', story_id: 1, value: true/false }, { key: 'bedroom', type: 'boolean' }]

  @column()
  public fileId: number

  @belongsTo(() => File, { foreignKey: 'fileId' })
  public file: BelongsTo<typeof File>

  @column()
  public audioUrl: string

  @column()
  public packId: number

  @belongsTo(() => Pack, { foreignKey: 'packId' })
  public pack: BelongsTo<typeof Pack>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
