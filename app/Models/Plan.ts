import { DateTime } from 'luxon'
import { BaseModel, Has<PERSON>any, column, hasMany, manyToMany, ManyToMany } from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'
import PlanFilter from './Filters/PlanFilter'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Subscription from './Subscription'
import PlanPricing from './PlanPricing'

export enum PlanType {
  COMMUNITY = 'community',
  GAME = 'game',
  PRESCHOOL = 'preschool',
  MUSIC = 'music',
}

// TODO: PlanStoryOrder

// stripe: plan & product
export default class Plan extends compose(BaseModel, Filterable) {
  public static $filter = () => PlanFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['title'],
    allowUpdates: true,
  })
  public handle: string

  @column()
  public description: string

  @column()
  public type: PlanType

  @column()
  public iconUrl: string | null

  @column()
  public thumbnailUrl: string | null

  @column()
  public stripeProductId: string | null

  @column({ prepare: (value) => JSON.stringify(value) })
  public region: string | Array<string>

  @column()
  public language: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  // show in app home page if published
  @column({ consume: (value: boolean) => Boolean(value) })
  public published: boolean

  @hasMany(() => PlanPricing)
  public pricings: HasMany<typeof PlanPricing>

  // for plans & subscriptions
  @manyToMany(() => Story, {
    pivotTable: 'plan_stories',
    pivotForeignKey: 'plan_id',
    pivotRelatedForeignKey: 'story_id',
    pivotColumns: ['level', 'is_featured', 'ordering', 'is_free'],
    pivotTimestamps: true,
    serializeAs: 'stories',
  })
  public planStories: ManyToMany<typeof Story>

  @hasMany(() => Subscription)
  public subscriptions: HasMany<typeof Subscription>

  // @computed({ serializeAs: 'has_redeemed' })
  // public get hasRedeemed() {
  //   return this.$extras.redeemed > 0 ? true : false
  // }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
