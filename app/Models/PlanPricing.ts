import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import Plan from './Plan'
import Subscription from './Subscription'

export default class PlanPricing extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public planId: number

  @column()
  public appstoreProductId: string

  @column()
  public playstoreProductId: string

  @column()
  public stripePriceId: string | null

  @column()
  public duration: 'month' | 'year' | undefined

  @column({ consume: (value: number) => Number(value) })
  public price: number

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  @column({ consume: (value: number) => Number(value) })
  public discount: number // in percentage

  @column.dateTime()
  public promotionEndedAt: DateTime

  @belongsTo(() => Plan)
  public plan: BelongsTo<typeof Plan>

  @hasMany(() => Subscription)
  public subscriptions: HasMany<typeof Subscription>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
