import { DateTime } from 'luxon'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'

export default class Preschool extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public description: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public region: string | Array<string>

  @column()
  public language: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  @hasMany(() => Story)
  public stories: HasMany<typeof Story>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
