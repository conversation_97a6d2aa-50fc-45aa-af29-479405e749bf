import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class PushNotification extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public body: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public recipients: string | number[]

  @column()
  public imageUrl: string

  @column()
  public scheduled: boolean

  @column.dateTime()
  public scheduledAt: DateTime

  @column()
  public sent: boolean

  @column()
  public status: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
