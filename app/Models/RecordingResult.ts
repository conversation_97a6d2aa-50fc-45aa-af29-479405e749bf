import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Chapter from './Chapter'
import User from './User'
import RecordingResultFilter from './Filters/RecordingResultFilter'
import File from './File'
import Story from './Story'
import Session from './Session'

export default class RecordingResult extends compose(BaseModel, Filterable) {
  public static $filter = () => RecordingResultFilter

  @column({ isPrimary: true })
  public id: number

  // text to compare
  @column()
  public category: string

  // pinyin: extra text, same sound but different word
  @column({ prepare: (value) => JSON.stringify(value) })
  public extraText: string

  // pinyin: text compared and remain
  @column({ prepare: (value) => JSON.stringify(value) })
  public remainingText: string

  @column()
  public pinyinHypothesis: string

  @column()
  public hypothesis: string

  @column()
  public hypothesisScore: number

  @column()
  public language: string

  @column()
  public device: string

  @column()
  public minimumVolume: string

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  public rawResult: object // json

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  public calibrations: string[] // json

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  public volumes: string[] // json

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isRepeat: boolean

  @column()
  public chapterId: number

  @belongsTo(() => Chapter)
  public chapter: BelongsTo<typeof Chapter>

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column()
  public sessionId: number

  @belongsTo(() => Session)
  public session: BelongsTo<typeof Session>

  @column()
  public fileId: number

  @belongsTo(() => File)
  public file: BelongsTo<typeof File>

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
