import { DateTime } from 'luxon'
import { <PERSON><PERSON>odel, <PERSON><PERSON><PERSON>To, Has<PERSON>any, belongsTo, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'
import User from './User'
import Child from './Child'
import RecordingResult from './RecordingResult'

export default class Session extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column()
  public gameScore: number

  @column()
  public fullGameScore: number

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public childId: number

  @belongsTo(() => Child)
  public child: BelongsTo<typeof Child>

  @hasMany(() => RecordingResult)
  public recordingResults: HasMany<typeof RecordingResult>

  // start/end session ?

  @column.dateTime()
  public endedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
