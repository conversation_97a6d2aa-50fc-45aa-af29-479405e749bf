import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import File from './File'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import SettingFilter from './Filters/SettingFilter'

export default class Setting extends compose(BaseModel, Filterable) {
  public static $filter = () => SettingFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public handle: string

  @column()
  public value: string

  @column()
  public status: string // active, inactive

  @column()
  public description: string

  @column()
  public type: string // system

  @column({ prepare: (values) => JSON.stringify(values) })
  public metadata: any // json

  @column()
  public fileId: number

  @belongsTo(() => File)
  public file: BelongsTo<typeof File>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
