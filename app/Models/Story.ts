import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  belongsTo,
  column,
  computed,
  hasMany,
  HasMany,
  hasOne,
  HasOne,
  manyToMany,
  ManyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'
import Chapter from './Chapter'
import StoryFilter from './Filters/StoryFilter'
import Pack from './Pack'
import StoryOrder from './StoryOrder'
import File from './File'
import PurchaseClickTracker from './PurchaseClickTracker'
import Preschool from './Preschool'
import BundleStory from './BundleStory'
import Plan from './Plan'
import Tag from './Tag'

export enum StoryType {
  COMMUNITY = 'community',
  GAME = 'game',
  PRESCHOOL = 'preschool',
  PACK = 'pack',
}

// activity has many stories
export default class Story extends compose(BaseModel, Filterable) {
  public static $filter = () => StoryFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['title'],
    allowUpdates: true,
  })
  public handle: string

  @column()
  public description: string | null

  @column()
  public language: string

  // difficulty
  @column()
  public level: number

  @column({ prepare: (value) => JSON.stringify(value) })
  public region: string | Array<string>

  @column()
  public thumbnailUrl: string | null

  @column()
  public previewImageUrl: string | null

  @column()
  public previewVideoId: number | null

  @belongsTo(() => File, { foreignKey: 'previewVideoId', serializeAs: 'preview_video' })
  public previewVideo: BelongsTo<typeof File>

  // break stories don't have options
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isBreak: boolean

  @column()
  public qrCode: string

  @column({ consume: (value: number) => Number(value) })
  public price: number

  @column({ consume: (value: number) => Number(value) })
  public compareAtPrice: number

  @column()
  public wordCount: number

  @column()
  public type: StoryType

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isCommunity: boolean

  @column()
  public communityReviewText: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public isFeatured: boolean

  @column()
  public isExclusive: boolean

  @column()
  public status: string

  @column()
  public packId: number

  @column()
  public preschoolId: number | null

  @column()
  public ordering: number

  @manyToMany(() => Plan, {
    pivotTable: 'plan_stories',
    pivotForeignKey: 'story_id',
    pivotRelatedForeignKey: 'plan_id',
  })
  public plans: ManyToMany<typeof Plan>

  @belongsTo(() => Pack)
  public pack: BelongsTo<typeof Pack>

  @belongsTo(() => Preschool)
  public preschool: BelongsTo<typeof Preschool>

  @hasMany(() => BundleStory)
  public bundleStory: HasMany<typeof BundleStory>

  @column()
  public defaultChapterId: number | null

  @hasOne(() => Chapter)
  public defaultChapter: HasOne<typeof Chapter>

  @column({ prepare: (value) => JSON.stringify(value) })
  public metadata: any

  @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_click_trackers' })
  public purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  @hasMany(() => Chapter)
  public chapters: HasMany<typeof Chapter>

  @hasMany(() => StoryOrder, { serializeAs: 'story_orders' })
  public storyOrders: HasMany<typeof StoryOrder>

  @manyToMany(() => Tag, {
    pivotTable: 'story_tags',
    pivotTimestamps: true
  })
  public tags: ManyToMany<typeof Tag>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @computed({ serializeAs: 'has_redeemed' })
  public get hasRedeemed() {
    return this.$extras.redeemed > 0 ? true : false
  }

  @computed({ serializeAs: 'purchase_clicks' })
  public get clicks() {
    return this.$extras.clicks
  }

  @computed({ serializeAs: 'plan_id' })
  public get planId() {
    return this.$extras.pivot_plan_id
  }

  @computed({ serializeAs: 'plan_level' })
  public get planLevel() {
    return this.$extras.pivot_level
  }

  @computed({ serializeAs: 'plan_featured' })
  public get planFeatured() {
    return this.$extras.pivot_is_featured
  }

  @computed({ serializeAs: 'plan_free' })
  public get planFree() {
    return Boolean(this.$extras.pivot_is_free)
  }
}
