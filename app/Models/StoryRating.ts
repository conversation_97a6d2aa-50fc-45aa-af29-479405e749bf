import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Story from './Story'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import StoryRatingFilter from './Filters/StoryRatingFilter'

export enum StoryRatingStatus {
  ASKING = 'asking',
  PENDING = 'pending',
  RATED = 'rated',
}

type RatingReason = {
  selected_options?: string[]
  others?: string
}

export default class StoryRating extends compose(BaseModel, Filterable) {
  public static $filter = () => StoryRatingFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column()
  public rating: number

  @column()
  public review: string

  @column({ prepare: (value) => JSON.stringify(value) })
  public reason: string | RatingReason

  @column()
  public count: number

  @column()
  public status: StoryRatingStatus

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
