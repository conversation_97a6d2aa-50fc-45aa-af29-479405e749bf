import { DateTime } from 'luxon'
import { BaseModel, column, manyToMany, ManyToMany } from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'

export enum TagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export default class Tag extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['name'],
    allowUpdates: true,
  })
  public slug: string

  @column()
  public status: TagStatus

  @column()
  public imageUrl: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Story, {
    pivotTable: 'story_tags',
  })
  public stories: ManyToMany<typeof Story>
}
