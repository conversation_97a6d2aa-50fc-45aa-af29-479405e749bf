import { DateTime } from 'luxon'
import {
  BaseModel,
  beforeFetch,
  beforeFind,
  BelongsTo,
  belongsTo,
  column,
  computed,
  ModelQueryBuilderContract,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import User from './User'
import Pack from './Pack'
import { isEmpty } from 'radash'
import { RiveInput } from './PackLevel'
import UserPackFilter from './Filters/UserPackFilter'
import { flatten } from 'lodash'

export type StoryStatus = {
  story_id: number
  child_id: number
  completed: boolean
}

export type LevelStatus = {
  child_id: number
  level: number
  completed: boolean
  triggered: boolean
}

export default class UserPack extends compose(BaseModel, Filterable) {
  public static $filter = () => UserPackFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public packId: number

  @belongsTo(() => Pack)
  public pack: BelongsTo<typeof Pack>

  @column()
  public currentLevel: number

  @column({ consume: (value: boolean) => Boolean(value) })
  public blocked: boolean

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  @column()
  public storyStatuses: Array<StoryStatus>

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  @column()
  public levelStatuses: Array<LevelStatus>

  @computed({ serializeAs: 'current_rive' })
  public get currentRive() {
    if (!isEmpty(this.pack?.packLevels) ?? false) {
      return this.pack.packLevels.find((packLevel) => packLevel.level == this.currentLevel)?.file
    }

    return null
  }

  @computed({ serializeAs: 'current_input' })
  public get currentInput() {
    let inputs: RiveInput[] = []
    if (!isEmpty(this.pack?.packLevels) ?? false) {
      inputs =
        this.pack.packLevels.find((packLevel) => packLevel.level == this.currentLevel)?.inputs ?? []
    }

    // input
    // [({ key: 'outdoor', type: 'boolean', story_id: 1 }, { key: 'bedroom', type: 'boolean', story_id: 2 })]

    if (inputs.length > 0) {
      if ((this.storyStatuses?.length ?? 0) > 0) {
        let finalInput: RiveInput[] = []
        inputs.forEach((input) => {
          const completedIndex = this.storyStatuses.findIndex(
            (status) =>
              input.story_id == status.story_id && status.child_id == this.user.activeChildId
          )

          if (completedIndex != -1) {
            finalInput.push({ ...input, value: true })
          } else {
            finalInput.push({ ...input, value: false })
          }
        })

        // finalInput
        // [({ key: 'outdoor', type: 'boolean', story_id: 1, value: true }, { key: 'bedroom', type: 'boolean', story_id: 2, value: fales })]

        return finalInput
      }

      return inputs
    }

    return null
  }

  @computed({ serializeAs: 'all_input' })
  public get allInputs() {
    let inputs: RiveInput[] = []
    if (!isEmpty(this.pack?.packLevels) ?? false) {
      inputs = flatten(this.pack.packLevels.map((packLevel) => packLevel.inputs))
    }

    if (inputs.length > 0) {
      if ((this.storyStatuses?.length ?? 0) > 0) {
        let finalInput: RiveInput[] = []
        inputs.forEach((input) => {
          const completedIndex = this.storyStatuses.findIndex(
            (status) =>
              input.story_id == status.story_id && status.child_id == this.user.activeChildId
          )

          if (completedIndex != -1) {
            finalInput.push({ ...input, value: true })
          } else {
            finalInput.push({ ...input, value: false })
          }
        })

        return finalInput
      }

      return inputs
    }

    return null
  }

  @column()
  public wordCount: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // For Multiple rows.
  @beforeFetch()
  public static notBlockedMultiple(query: ModelQueryBuilderContract<typeof UserPack>) {
    query.where('blocked', false)
  }

  // For single rows
  @beforeFind()
  public static notBlockedSingle(query: ModelQueryBuilderContract<typeof UserPack>) {
    query.where('blocked', false)
  }
}
