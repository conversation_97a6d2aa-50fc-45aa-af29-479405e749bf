import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'

type UserSettingMetadata = {
  repeat_animation: number
}

export default class UserSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public receiveUpdate: boolean

  @column()
  public userId: number

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column({ prepare: (values) => JSON.stringify(values) })
  public metadata: UserSettingMetadata

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
