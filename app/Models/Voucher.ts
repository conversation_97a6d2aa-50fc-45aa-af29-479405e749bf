import { DateTime } from 'luxon'
import {
  BaseModel,
  Bel<PERSON>sTo,
  HasMany,
  belongsTo,
  column,
  computed,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import Story from './Story'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import VoucherFilter from './Filters/VoucherFilter'
import StoryOrder from './StoryOrder'
import Preschool from './Preschool'

export default class Voucher extends compose(BaseModel, Filterable) {
  public static $filter = () => VoucherFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public voucherCode: string

  @column()
  public description: string

  @column({ consume: (value: number) => Number(value) })
  public discount: number

  @column()
  public maxOfUsed: number

  @column.dateTime()
  public expiredAt: DateTime

  @column()
  public storyId: number

  @belongsTo(() => Story)
  public story: BelongsTo<typeof Story>

  @column()
  public preschoolId: number

  @belongsTo(() => Preschool)
  public preschool: BelongsTo<typeof Preschool>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => StoryOrder)
  public storyOrders: HasMany<typeof StoryOrder>

  @computed({ serializeAs: 'total_of_used' })
  public get totalOfUsed() {
    return this.$extras.total_of_used
  }
}
