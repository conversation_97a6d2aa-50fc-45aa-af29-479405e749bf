import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  belongsTo,
  column,
  computed,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Transaction from './Transaction'
import BigNumber from 'bignumber.js'

export default class Wallet extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @column()
  public currency: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @hasMany(() => Transaction)
  public transactions: HasMany<typeof Transaction>

  @computed()
  public get balance(): number {
    return new BigNumber(this.$extras.balance ?? 0).decimalPlaces(2).toNumber()
  }
}
