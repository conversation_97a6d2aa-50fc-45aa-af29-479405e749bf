{"type": "service_account", "project_id": "brookiekids", "private_key_id": "83552b8f95d713017c1321c235471549b72c62fd", "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCid+J1EfPaYMjI\n/tbbOHVawoSECyxSZLOUQVRCoK4g8GkhE2LkUBrtWWeKs5zB7Wnn+4t88Xgunq9g\nIY2K4BrL1qI1WNfEX7MN9drA6+KrXGvefX8XLqgns+5Z3EmEFwWsSvuROea04WjM\nVhylJK3awlSYXiju/j0OykTylmpvP0YdoU91zxMYCyazKqETw39pFXFx0MiZ+/6r\nmVNYfGlsHsErH3pKi6zkeLpbN8zvlNwkuae3ZWm1z+6isAq9NCwr2neiYhWcQZYE\nlcerRcm7y+nEXv2xD+32mHDOzQyWNZxWKxmahv6q89pXAALrD7bIIQ/aLaP4uk8R\nmiY+ya/vAgMBAAECggEAGYXa2ghVdieBM24xY6P1b6oiNjopHtJ6EZ7HtWXXpQ4P\nfeY1QWfCKypB5Vy9EgwReKOgMKMgfbZpF6F9wmTdYMtZlSbdUpi0IMvQL0F/TOtK\neNvfNM2cFgyis5QzKmCRgBUReymM8Dp/A6a9fkYKV9TyhDM6m/ztq56kATYXB125\nuUjMr1RhXfA8uEpqeZPalZ5Ir5x5m7pkrd0lUdSivNXMKaCmR1V3HeQXAYuky86c\nPKnqA9yfVYyGpNLMxPXZurf5BLRGhjJu2+trUe5kXxQZHFHvVQyZF1FggdTf1CoJ\nJguaLnVPAS7Mclt1rKHqHIx4vFIGdXmv/wX6ljqLrQKBgQDO1FgCauIXQmifuOUp\nzG0Mgdg5xuCsuenH1IeLs37Xtpg8VI602YwmAhurk5i9fsdE2njVHluO7h2U4CbJ\nPXye09mul+BJ89PZhR7j0BLNtFb20g+byUhDCtXYFGXYp6OqlG62sHJQOsoaYiaY\noO58OC15KiLaET37hfUeO6uP9QKBgQDJF7eHf2SRc/+7C3MpYETzvkCHhmgqSsSP\n+b+VUV6tu6EGeZL/wDNFzZVeG+LI4wLtAZGBgWNjsjqh1eUny1EXGG4Zc2f3of1Y\nFVUqtqRZmQTG+XH9thnYAPI0lzWXvXCUcq7d0lwO10Jc+fx8oBlgsa3cBGDnCKon\nV6CQtCFF0wKBgCSRUHok8ZJRMHRE2Wd5eDJE5rjYW7LOL/IftlNRyfRTmTjZbl21\nNN0ndELyloeyWYLn6YMsmd18ZgiDVPgxgc4HAK6otYP9sw/7REnc6OYro1CCNTZC\n8R7BVKued59FjF9FXQm5yow6yK6/ebl0gpW9zPRmKcFKWR4WQg81V5DdAoGAb+2x\ni8vOsF8+kl/XI8qf1oOo60ZWqGOhcPn0NyppZo4vHKmCtrPGVi4N26nt2x7wO/dJ\nhCn+pjlIBVhHCpVSd2ZzsdrYna8ekm6dTv8GD5DqZc3mMu+vBtnP9pHLlMAu75G9\n3139O415NMM4VC3Qsk9zlCkjvrTF8MwHZUO92V0CgYBCfLA0eoYbjtZIJl5J+6bT\n3uplOCRz9Z780QBRm8X7ZFA6yA0ns9gfrLkFU0IOk+uNtY+iMtBo2f/QEU+gfEIY\nD+xr0BrMK/mu0c1RqgPLTcQyntnYBWZiJ4e2u1SMKuFdi4p2XmXDb1BIS8v5L9J8\nQdCAYGXKy9kE0hZhRBMiiA==\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "102634187062993763306", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-dmh47%40brookiekids.iam.gserviceaccount.com"}