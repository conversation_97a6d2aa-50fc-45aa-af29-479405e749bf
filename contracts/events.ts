/**
 * Contract source: https://git.io/JfefG
 *
 * Feel free to let us know via PR, if you find something broken in this contract
 * file.
 */

import stripe from 'stripe'

declare module '@ioc:Adonis/Core/Event' {
  /*
  |--------------------------------------------------------------------------
  | Define typed events
  |--------------------------------------------------------------------------
  |
  | You can define types for events inside the following interface and
  | AdonisJS will make sure that all listeners and emit calls adheres
  | to the defined types.
  |
  | For example:
  |
  | interface EventsList {
  |   'new:user': UserModel
  | }
  |
  | Now calling `Event.emit('new:user')` will statically ensure that passed value is
  | an instance of the the UserModel only.
  |
  */
  interface EventsList {
    'stripe:product-created': stripe.Product
    'stripe:price-created': stripe.Price
    'stripe:product-updated': stripe.Product
    'stripe:price-updated': stripe.Price
    'stripe:customer-created': stripe.Customer
    'stripe:subscription-created': stripe.Subscription
    'stripe:plan-purchased': stripe.Invoice
    'stripe:plan-paused': stripe.Invoice
    'stripe:plan-failed': stripe.Invoice
    'plan:trial': { email: string }
  }
}
