import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      // table.string('username', 255).notNullable().unique()
      table.string('email', 255).notNullable().unique()
      table.string('password', 180).notNullable()
      table.string('name')
      table.string('region')
      table.string('phone')
      table.string('address_1')
      table.string('address_2')
      table.string('postcode')
      table.string('remember_me_token').nullable()
      table.boolean('is_anonymous').defaultTo(true) // generated users
      table.boolean('blocked').defaultTo(false) // generated users
      table
        .integer('user_group_id')
        .unsigned()
        .references('id')
        .inTable('user_groups')
        .onDelete('SET NULL')
      table.boolean('verified').nullable()
      table.boolean('phone_verified').nullable()

      /**
       * Uses timestampz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).notNullable()
      table.timestamp('updated_at', { useTz: true }).notNullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
