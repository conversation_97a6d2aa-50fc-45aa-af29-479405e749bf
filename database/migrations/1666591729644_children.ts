import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Children extends BaseSchema {
  protected tableName = 'children'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('name')
      table.integer('english_level')
      table.integer('chinese_level')
      table.timestamp('birthdate')
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('SET NULL')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
