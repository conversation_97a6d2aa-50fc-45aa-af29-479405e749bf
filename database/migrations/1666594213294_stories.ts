import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('title')
      table.string('handle')
      table.text('description')
      table.string('language')
      table.integer('level')
      table.string('region')
      table.string('thumbnail_url')
      table.boolean('is_break').defaultTo(false)
      table.string('qr_code')
      table.boolean('is_community')
      table.string('community_review_text')
      table.integer('pack_id').unsigned().references('id').inTable('packs')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
