import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('default_chapter_id')
        .unsigned()
        .references('id')
        .inTable('chapters')
        .onDelete('CASCADE')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('default_chapter_id')
      table.dropColumns('default_chapter_id')
    })
  }
}
