import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'feedback_histories'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.text('content')
      table.integer('from_user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('to_user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table
        .integer('feedback_id')
        .unsigned()
        .references('id')
        .inTable('feedbacks')
        .onDelete('CASCADE')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
