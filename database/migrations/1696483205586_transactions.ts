import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'transactions'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('wallet_id').unsigned().references('id').inTable('wallets').onDelete('set NULL')
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('set NULL')
      table.string('title')
      table.string('description')
      table.string('type', 10)
      table.string('status', 10).defaultTo('pending')
      table.decimal('amount', 6, 2)
      table.decimal('amount_in', 6, 2)
      table.decimal('amount_out', 6, 2)
      table.string('remark')
      table.string('txn_hash').unique()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
