import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'credits'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('price', 8, 2)
      table.decimal('compare_at', 8, 2)
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, table => {
      table.dropColumn('price')
      table.dropColumn('compare_at')
    })
  }
}
