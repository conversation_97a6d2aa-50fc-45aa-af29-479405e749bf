import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type', ['community', 'game', 'pack', 'preschool'])
      table.boolean('is_exclusive').defaultTo(false)
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns('type', 'is_exclusive')
    })
  }
}
