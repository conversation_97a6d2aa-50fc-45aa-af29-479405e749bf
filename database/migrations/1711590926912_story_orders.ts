import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'story_orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['story_id', 'user_id'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['story_id', 'user_id'])
    })
  }
}
