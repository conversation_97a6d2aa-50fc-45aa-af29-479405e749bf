import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'subscriptions'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('plan_id').unsigned().references('id').inTable('plans')
      table.string('provider')
      table.string('provider_plan_id')
      table.string('provider_subscription_id')
      table.integer('user_id').unsigned().references('id').inTable('users')
      table.string('provider_user_id')
      table.string('status')
      table.timestamp('start_date')
      table.timestamp('end_date')
      table.timestamp('cycle_start_date')
      table.timestamp('cycle_end_date')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
