import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'plan_pricings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dateTime('promotion_ended_at')
      table.decimal('discount', 8, 4)
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('promotion_ended_at')
      table.dropColumn('discount')
    })
  }
}
