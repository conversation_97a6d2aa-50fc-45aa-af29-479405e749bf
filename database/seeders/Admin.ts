import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Admin from 'App/Models/Admin'
import User from 'App/Models/User'

export default class extends BaseSeeder {
  public async run() {
    const user = await User.create({
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      password: 'abcd@1234',
    })

    await Admin.create({
      userId: user.id,
      type: 0, // super admin
    })
  }
}
