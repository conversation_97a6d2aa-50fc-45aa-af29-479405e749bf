import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Pack from 'App/Models/Pack'
import Story from 'App/Models/Story'
import _ from 'lodash'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const allStories = await Story.query()
    for (let story of allStories) {
      if (_.isArray(story.region)) continue
      story.region = [story.region]
      await story.save()
    }

    const allPacks = await Pack.query()
    for (let pack of allPacks) {
      if (_.isArray(pack.region)) continue
      pack.region = [pack.region]
      await pack.save()
    }
  }
}
