import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Credit from 'App/Models/Credit'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    // const creditOptions = [5, 10, 20, 25, 50, 100]
    // for (const i in creditOptions) {
    //   const option = creditOptions[i]
    //   await Credit.firstOrCreate(
    //     {
    //       amount: option,
    //       currency: 'SGD',
    //     },
    //     {
    //       title: `${option.toFixed(2)} SGD`,
    //       description: 'Credits never expire',
    //       rank: Number(i) + 1,
    //     }
    //   )
    // }

    await Credit.firstOrCreate(
      {
        amount: 5,
        handle: 'fivecredits',
        currency: 'SGD',
      },
      {
        title: `$5 credits`,
        rank: 1,
      }
    )

    await Credit.firstOrCreate(
      {
        amount: 10,
        handle: '20credits',
        currency: 'SGD',
      },
      {
        title: `$10 for 20 credits`,
        rank: 2,
      }
    )

    await Credit.firstOrCreate(
      {
        amount: 25,
        handle: '50credits',
        currency: 'SGD',
      },
      {
        title: `$25 for 50 credits`,
        rank: 3,
      }
    )
  }
}
