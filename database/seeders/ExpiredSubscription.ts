import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Subscription from 'App/Models/Subscription'
import Plan from 'App/Models/Plan'
import StoryOrder from 'App/Models/StoryOrder'
import { DateTime } from 'luxon'

export default class extends BaseSeeder {
  public async run() {
    // find all expired subscription
    const subscriptions = await Subscription.query().where('cycle_end_date', '<', new Date())
    console.log('expired subscriptions', subscriptions.length)
    for (let subscription of subscriptions) {
      if (subscription.status != 'canceled') {
        // update subscription
        subscription.merge({
          status: 'canceled',
          endDate: DateTime.now(),
        })
        await subscription.save()
      }

      // find all story order & block it
      const plan = await Plan.query()
        .where('id', subscription.planId)
        .preload('planStories')
        .first()

      if (!plan) {
        // NOTE: should not happen
        return
      }

      for (let story of plan.planStories) {
        await StoryOrder.query().where('blocked', false)
          .where('user_id', subscription.userId)
          .where('story_id', story.id)
          .update({ blocked: true })
      }
    }
  }
}
