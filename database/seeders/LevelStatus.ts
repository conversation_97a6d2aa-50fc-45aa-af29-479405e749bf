import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Pack from 'App/Models/Pack'
import PackLevel from 'App/Models/PackLevel'
import Story from 'App/Models/Story'
import UserPack, { StoryStatus } from 'App/Models/UserPack'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const packs = await Pack.query()
    const userPacks = await UserPack.query().whereNotNull('story_statuses')
    for (const pack of packs) {
      const packLevels = await PackLevel.query().where('pack_id', pack.id).orderBy('level', 'asc')
      for (const packLevel of packLevels) {
        console.log(pack.title, packLevel.level)
        const stories = await Story.query()
          .where('pack_id', pack.id)
          .where('level', packLevel.level)
        const storyIds = stories.map((story) => story.id)
        for (const userPack of userPacks) {
          if (userPack.storyStatuses.length < stories.length) continue

          const storiesCompleted = userPack.storyStatuses.filter((item) =>
            storyIds.includes(item.story_id)
          )

          const childList: {
            [key: string]: StoryStatus[]
          } = {}
          for (let storyCompleted of storiesCompleted) {
            if (childList[storyCompleted.child_id] == undefined) {
              childList[storyCompleted.child_id] = [storyCompleted]
            } else {
              childList[storyCompleted.child_id].push(storyCompleted)
            }
          }
          // console.log(userPack.id, childList)

          const currentLevelStatusList = userPack.levelStatuses ?? []
          // console.log(currentLevelStatusList)
          for (const childId in childList) {
            // console.log(childId, childList[childId].length, stories.length)
            if (childList[childId].length == stories.length) {
              if (
                !currentLevelStatusList.some(
                  (status) => status.child_id == Number(childId) && status.level == packLevel.level
                )
              ) {
                currentLevelStatusList.push({
                  child_id: Number(childId),
                  level: packLevel.level,
                  completed: true,
                  triggered: false,
                })
              }
            }
          }

          await Database.transaction(async (trx) => {
            userPack.levelStatuses = currentLevelStatusList
            userPack.useTransaction(trx)
            await userPack.save()
          })
        }
      }
    }
  }
}
