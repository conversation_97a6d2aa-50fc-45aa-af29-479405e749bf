import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Story from 'App/Models/Story'
import StoryOrder from 'App/Models/StoryOrder'
import Subscription from 'App/Models/Subscription'

export default class extends BaseSeeder {
  public async run() {
    await Database.transaction(async (trx) => {
      // write your database queries inside the run method
      const subscriptions = await Subscription.query()
        .whereIn('status', ['trialing', 'active'])
        .useTransaction(trx)
      const storyList = [141, 142]
      const stories = await Story.query().whereIn('id', storyList).useTransaction(trx)

      for (let story of stories) {
        // add story to plan story
        await Database.table('plan_stories').insert({
          plan_id: 1,
          story_id: story.id,
          is_free: false,
          created_at: new Date(),
          updated_at: new Date(),
        })

        // loop each user & create story order
        for (let subscription of subscriptions) {
          await StoryOrder.firstOrCreate(
            {
              storyId: story.id,
              userId: subscription.userId,
            },
            {
              storyId: story.id,
              userId: subscription.userId,
            },
            { client: trx }
          )
        }
      }
    })
  }
}
