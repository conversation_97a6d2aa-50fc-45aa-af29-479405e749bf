import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User, { generateReferralCode } from 'App/Models/User'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const users = await User.query()

    await Database.transaction(async (trx) => {
      for (const user of users) {
        if (!user.referralCode) {
          let repeat = false
          let referralCode
          do {
            referralCode = generateReferralCode(8)
            if (await User.findBy('referral_code', referralCode)) {
              repeat = true
            } else {
              repeat = false
            }
          } while (repeat || !referralCode)
          user.referralCode = referralCode
          user.useTransaction(trx)
          await user.save()
        }
      }
    })
  }
}
