import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Story, { StoryType } from 'App/Models/Story'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const stories = await Story.query()

    await Database.transaction(async (trx) => {
      for (let story of stories) {
        if (story.isCommunity) {
          story.type = StoryType.COMMUNITY
        } else if (story.preschoolId) {
          story.type = StoryType.PRESCHOOL
        } else if (story.packId) {
          story.type = StoryType.PACK
        } else {
          story.type = StoryType.GAME
        }

        story.useTransaction(trx)
        await story.save()
      }
    })
  }
}
