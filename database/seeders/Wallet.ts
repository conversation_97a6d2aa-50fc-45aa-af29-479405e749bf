import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'
import Wallet from 'App/Models/Wallet'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const users = await User.query()
    for (const user of users) {
      const findWallet = await Wallet.findBy('user_id', user.id)
      if (findWallet) continue

      await Database.transaction(async (trx) => {
        const newWallet = new Wallet()
        newWallet.userId = user.id
        newWallet.currency = 'SGD'

        newWallet.useTransaction(trx)
        await newWallet.save()
      })
    }
  }
}
