{"name": "brookiekids-be", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "lint": "eslint . --ext=.ts", "format": "prettier --write ."}, "eslintConfig": {"extends": ["plugin:adonis/typescriptApp", "prettier"], "plugins": ["prettier"], "rules": {"eqeqeq": "off", "prettier/prettier": ["error"]}}, "eslintIgnore": ["build"], "prettier": {"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100}, "devDependencies": {"@adonisjs/assembler": "^5.9.3", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.2.2", "adonis-preset-ts": "^2.1.0", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^4.2.1", "pino-pretty": "^9.1.1", "prettier": "^2.7.1", "typescript": "~4.6", "youch": "^3.2.2", "youch-terminal": "^2.1.5"}, "dependencies": {"@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.8.0", "@adonisjs/drive-s3": "^1.3.2", "@adonisjs/lucid": "^18.4.0", "@adonisjs/lucid-slugify": "^2.2.1", "@adonisjs/redis": "7.3.4", "@adonisjs/repl": "^3.1.0", "@aws-sdk/client-mediaconvert": "^3.341.0", "@ericblade/quagga2": "^1.8.2", "@googleapis/androidpublisher": "^8.1.0", "@mailerlite/mailerlite-nodejs": "^1.1.0", "@types/crypto-js": "^4.1.2", "@types/google-spreadsheet": "^3.3.2", "@types/lodash": "^4.14.194", "@types/node-cron": "^3.0.7", "@types/randomstring": "^1.1.8", "adonis-lucid-filter": "^4.1.1", "apple-auth": "^1.0.9", "aws-sdk": "^2.1238.0", "axios": "^1.1.3", "bullmq": "^5.12.11", "crypto-js": "^4.1.1", "csv-parse": "^5.5.2", "firebase-admin": "^11.7.0", "fs": "^0.0.1-security", "google-spreadsheet": "3.3.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "luxon": "^3.5.0", "mailersend": "^2.1.1", "microsoft-cognitiveservices-speech-sdk": "^1.24.0", "mysql2": "^2.3.3", "node-cron": "^3.0.2", "phc-argon2": "^1.1.3", "pinyin": "^3.0.0-alpha.5", "proxy-addr": "^2.0.7", "query-string": "^7.1.1", "radash": "^10.8.1", "randomstring": "^1.2.3", "reflect-metadata": "^0.1.13", "source-map-support": "^0.5.21", "stripe": "^15.6.0", "uuid": "^9.0.0", "ws": "^8.13.0"}}