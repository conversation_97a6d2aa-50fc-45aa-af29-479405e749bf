import { ApplicationContract } from '@ioc:Adonis/Core/Application'
import { Queue, Worker } from 'bullmq'

export default class BullProvider {
  constructor(protected app: ApplicationContract) {}

  public register() {
    this.app.container.singleton('Bull/Queue', () => {
      return {
        queue: (name: string) =>
          new Queue(name, {
            connection: this.app.config.get('redis.connections.local'),
          }),
        worker: (name: string, processor: (job: any) => Promise<any>) =>
          new Worker(name, processor, {
            connection: this.app.config.get('redis.connections.local'),
          }),
      }
    })
  }

  public async boot() {
    // All bindings are ready, feel free to use them
  }

  public async ready() {
    // App is ready
  }

  public async shutdown() {
    // Cleanup, since app is going down
  }
}
