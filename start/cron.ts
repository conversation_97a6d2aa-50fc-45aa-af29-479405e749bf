/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/

import User from 'App/Models/User'
import axios from 'axios'
import _, { isEmpty } from 'lodash'
import { DateTime } from 'luxon'
import cron from 'node-cron'
import Subscription from 'App/Models/Subscription'
import Plan from 'App/Models/Plan'
import StoryOrder from 'App/Models/StoryOrder'

const findInactiveUsers = async (region: string, groupId: string) => {
  const today = DateTime.now()
  const lastWeek = today.minus({ weeks: 1 })
  const thisMonth = today.minus({ months: 1 })
  let users: User[] = []
  if (region === 'tw') {
    // taiwan users
    users = await User.query()
      .where('region', region)
      .whereBetween('created_at', [thisMonth.toISO(), lastWeek.toISO()])
      .whereDoesntHave('files', (query) => {
        query.where('created_at', '>', lastWeek.toISO())
      })
  } else {
    // rest of the world
    users = await User.query()
      .whereNot('region', 'tw')
      .whereBetween('created_at', [thisMonth.toISO(), lastWeek.toISO()])
      .whereDoesntHave('files', (query) => {
        query.where('created_at', '>', lastWeek.toISO())
      })
  }

  try {
    const mailerliteToken = process.env.MAILERLITE_API
    const { data: subscribers } = await axios.get(
      `https://connect.mailerlite.com/api/groups/${groupId}/subscribers?filter[status]=active`,
      {
        headers: {
          Authorization: `Bearer ${mailerliteToken}`,
        },
      }
    )

    let newEmails: User[] = users

    if (!isEmpty(subscribers.data)) {
      // find unique value in users
      newEmails = users.filter(
        (user) =>
          (subscribers.data as any[]).findIndex((subscriber) => subscriber.email === user.email) ===
          -1
      )
    }

    const mailerLiteBulkBody: { method: string; path: string; body: any }[] = []

    // subscribe these new emails
    newEmails.map((user) => {
      mailerLiteBulkBody.push({
        method: 'POST',
        path: `api/subscribers`,
        body: {
          email: user.email,
          groups: [groupId],
        },
      })
    })

    // split to chunks of 50
    // because mailerlite max 50
    const chunkedBody = _.chunk(mailerLiteBulkBody, 50)
    chunkedBody.forEach(async (body) => {
      // TODO: if error?
      const result = await axios.post(
        'https://connect.mailerlite.com/api/batch',
        {
          requests: body,
        },
        {
          headers: {
            Authorization: `Bearer ${mailerliteToken}`,
          },
        }
      )
      console.log(result)
    })
  } catch (error) {
    console.error(error)
  }
}

const sgGroupId = '71361047148103245' // Haven't started after 1 week
const twGroupId = '79585755607860783' // TAIWAN MVT 3. Haven't Tried after 1 week
// const twGroupId = '80333196757042414' // debug: Testing (Taiwan, no activity user)
// const sgGroupId = '80332793970689930' // debug: Testing (Taiwan New Users)

// run this daily midnight
if (process.env.RUN_CRON) {
  cron.schedule('0 0 * * *', async () => {
    await findInactiveUsers('tw', twGroupId)
    await findInactiveUsers('sg', sgGroupId)
  })

  // Run remove subscription job daily at 1 AM
  cron.schedule('0 1 * * *', async () => {
    console.log('Running remove subscription cron job at', DateTime.now().toISO())

    try {
      // Find subscriptions where cycle_end_date has exceeded and provider_subscription_id is null
      const currentDate = DateTime.now()
      const expiredSubscriptions = await Subscription.query()
        .where('cycle_end_date', '<', currentDate.toSQL())
        .whereNull('provider_subscription_id')
        .where('status', '!=', 'canceled')

      console.log(`Found ${expiredSubscriptions.length} expired subscriptions to process`)

      for (const subscription of expiredSubscriptions) {
        try {
          // Get user for logging purposes
          const user = await User.find(subscription.userId)
          const userEmail = user ? user.email : `User ID: ${subscription.userId}`

          // Update subscription
          subscription.merge({
            status: 'canceled',
            endDate: currentDate,
            // cycleEndDate: currentDate,
          })
          await subscription.save()
          console.log(`Canceled subscription ID: ${subscription.id} for ${userEmail}`)

          // Find all story order & deactivate it
          const plan = await Plan.query()
            .where('id', subscription.planId)
            .preload('planStories')
            .first()

          if (!plan) {
            console.log(`No plan found for subscription: ${subscription.id}, user: ${userEmail}`)
            continue
          }

          for (let story of plan.planStories) {
            await StoryOrder.updateOrCreate(
              {
                storyId: story.id,
                userId: subscription.userId,
              },
              {
                storyId: story.id,
                userId: subscription.userId,
                blocked: true,
              }
            )
          }
          console.log(`Updated story orders for subscription ID: ${subscription.id}, user: ${userEmail}`)
        } catch (error) {
          console.error(`Error processing subscription ID: ${subscription.id}:`, error)
        }
      }
      console.log('Remove subscription cron job completed at', DateTime.now().toISO())
    } catch (error) {
      console.error('Error in remove subscription cron job:', error)
    }
  })
}
